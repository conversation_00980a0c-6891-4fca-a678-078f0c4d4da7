# Quiz Management Module

This module contains all quiz-related functionality for the Teacher Assistant application.

## Structure

- `quiz_management/services/`: Quiz business logic
  - `quiz_service.py`: Quiz CRUD operations and database management
  - `quiz_taking_service.py`: Flask server for student quiz interface
- `gui/views/quizzes.py`: Quiz UI components (integrated into main GUI)

## Features

- **Quiz Creation**: Teachers can create multiple-choice quizzes with questions and options
- **Question Management**: Add, edit, and delete questions within quizzes
- **QR Code Generation**: Generate QR codes for students to access quizzes
- **Student Quiz Interface**: Mobile-friendly web interface for students to take quizzes
- **Results Management**: View and manage quiz submissions and student answers
- **Class/Subject Integration**: Associate quizzes with specific classes and subjects

## Usage

The quiz management module is integrated into the main Teacher Assistant application through the GUI views. Teachers can access quiz functionality through the main application interface.

For standalone quiz server operation, use:

```bash
python run_quiz_server.py
```

## Database Schema

The module creates and manages the following database tables:

- `quizzes`: Quiz metadata (title, description, class, subject)
- `quiz_questions`: Individual questions within quizzes
- `quiz_options`: Answer options for each question
- `quiz_submissions`: Student quiz submissions
- `quiz_answers`: Individual student answers to questions
