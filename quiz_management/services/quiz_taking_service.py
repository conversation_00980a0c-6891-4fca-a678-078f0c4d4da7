from flask import Flask, request, render_template_string
from quiz_management.services.quiz_service import get_quiz_with_questions, submit_quiz
from gui.utils.qr_code import is_quiz_server_running



app = Flask(__name__)

HTML_QUIZ_FORM = '''
<!doctype html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Quiz - {{quiz_title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            padding: 40px;
            position: relative;
            overflow: hidden;
        }
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
        }
        .quiz-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 3px solid #f1f3f4;
            position: relative;
        }
        .quiz-title {
            color: #2c3e50;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .quiz-description {
            color: #6c757d;
            font-size: 1.2rem;
            line-height: 1.6;
        }
        .progress-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        .progress-text {
            font-size: 1.1rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #28a745);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        .question-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            padding: 35px;
            margin-bottom: 35px;
            border: 2px solid #e9ecef;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .question-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 6px;
            height: 100%;
            background: linear-gradient(180deg, #007bff, #0056b3);
        }
        .question-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.12);
        }
        .question-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }
        .question-number {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            font-weight: 700;
            font-size: 1.1rem;
            padding: 12px 18px;
            border-radius: 50px;
            margin-right: 20px;
            min-width: 50px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }
        .question-text {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.6;
            flex: 1;
        }
        .options-container {
            margin-top: 25px;
        }
        .option {
            margin-bottom: 15px;
            position: relative;
        }
        .option input[type="checkbox"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }
        .option label {
            font-size: 1.1rem;
            cursor: pointer;
            padding: 18px 25px;
            border-radius: 15px;
            display: block;
            transition: all 0.3s ease;
            border: 2px solid #e9ecef;
            background: #ffffff;
            position: relative;
            padding-left: 60px;
            min-height: 24px;
            line-height: 1.5;
        }
        .option label::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            border: 2px solid #dee2e6;
            border-radius: 4px;
            background: white;
            transition: all 0.3s ease;
        }
        .option label::after {
            content: '✓';
            position: absolute;
            left: 23px;
            top: 50%;
            transform: translateY(-50%) scale(0);
            width: 14px;
            height: 14px;
            color: white;
            font-weight: bold;
            font-size: 12px;
            line-height: 14px;
            text-align: center;
            transition: transform 0.2s ease;
        }
        .option input[type="checkbox"]:checked + label {
            background: linear-gradient(135deg, #e3f2fd, #f0f8ff);
            border-color: #007bff;
            color: #0056b3;
            font-weight: 600;
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0,123,255,0.15);
        }
        .option input[type="checkbox"]:checked + label::before {
            border-color: #007bff;
            background: #007bff;
        }
        .option input[type="checkbox"]:checked + label::after {
            transform: translateY(-50%) scale(1);
        }
        .option label:hover {
            background: #f8f9fa;
            border-color: #007bff;
            transform: translateX(3px);
            box-shadow: 0 2px 10px rgba(0,123,255,0.1);
        }
        .student-info {
            background: linear-gradient(135deg, #e8f4fd, #f0f8ff);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            border: 2px solid #b3d9ff;
            position: relative;
        }
        .student-info::before {
            content: '👤';
            position: absolute;
            top: -10px;
            left: 30px;
            background: white;
            padding: 5px 10px;
            border-radius: 50px;
            font-size: 1.2rem;
        }
        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: white;
            width: 100%;
        }
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.15);
            transform: translateY(-1px);
            outline: none;
        }
        select.form-control {
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }
        select.form-control:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }
        .btn-submit {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            border-radius: 15px;
            padding: 18px 40px;
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            width: 100%;
            margin-top: 30px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }
        .btn-submit::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        .btn-submit:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(40,167,69,0.4);
        }
        .btn-submit:hover::before {
            left: 100%;
        }
        .error-message {
            color: #dc3545;
            font-size: 1rem;
            margin-top: 10px;
            display: none;
            padding: 10px;
            background: #f8d7da;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
        }
        .quiz-closed {
            text-align: center;
            padding: 60px;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 25px;
            }
            .quiz-title {
                font-size: 2rem;
            }
            .question-text {
                font-size: 1.2rem;
            }
            .option label {
                padding: 15px 20px;
                padding-left: 55px;
                font-size: 1rem;
            }
            .question-header {
                flex-direction: column;
                align-items: flex-start;
            }
            .question-number {
                margin-bottom: 15px;
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        {% if quiz_closed %}
        <div class="quiz-closed">
            <h2>Quiz Access Closed</h2>
            <p>This quiz is no longer available for taking.</p>
        </div>
        {% else %}
        <div class="quiz-header">
            <h1 class="quiz-title">{{quiz_title}}</h1>
            {% if quiz_description %}
            <p class="quiz-description">{{quiz_description}}</p>
            {% endif %}
            <p class="text-muted">{{question_count}} questions</p>
        </div>

        <form method="POST" id="quizForm">
            <input type="hidden" name="quiz_id" value="{{quiz_id}}">

            <div class="student-info">
                <label for="student_name" class="form-label"><strong>Select Your Name</strong></label>
                {% if student_names %}
                <select class="form-control" id="student_name" name="student_name" required>
                    <option value="">-- Select your name --</option>
                    {% for student_name in student_names %}
                    <option value="{{student_name}}">{{student_name}}</option>
                    {% endfor %}
                </select>
                {% else %}
                <select class="form-control" id="student_name" name="student_name" required disabled>
                    <option value="">No students enrolled</option>
                </select>
                {% endif %}
                <div class="error-message" id="nameError">Please select your name</div>
            </div>

            <div class="progress-container">
                <div class="progress-text">Quiz Progress</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%" id="progressFill"></div>
                </div>
                <div id="progressText">0 of {{question_count}} questions answered</div>
            </div>

            {% for question in questions %}
            <div class="question-card" data-question="{{loop.index}}">
                <div class="question-header">
                    <div class="question-number">{{loop.index}}</div>
                    <div class="question-text">{{question.question_text}}</div>
                </div>

                <div class="options-container">
                    {% for option in question.options %}
                    <div class="option">
                        <input type="checkbox" id="q{{question.id}}_opt{{option.id}}"
                               name="question_{{question.id}}" value="{{option.id}}"
                               onchange="updateProgress();">
                        <label for="q{{question.id}}_opt{{option.id}}">{{option.option_text}}</label>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}



            <button type="submit" class="btn btn-submit" id="submitBtn">
                🚀 Submit Quiz
            </button>
        </form>
        {% endif %}
    </div>

    <script>
        let totalQuestions = {{question_count}};



        function updateProgress() {
            // Count questions that have at least one checkbox checked
            const questionCards = document.querySelectorAll('.question-card');
            let answeredQuestions = 0;

            questionCards.forEach(card => {
                const checkboxes = card.querySelectorAll('input[type="checkbox"]:checked');
                if (checkboxes.length > 0) {
                    answeredQuestions++;
                }
            });

            const progressPercentage = (answeredQuestions / totalQuestions) * 100;

            document.getElementById('progressFill').style.width = progressPercentage + '%';
            document.getElementById('progressText').textContent =
                answeredQuestions + ' of ' + totalQuestions + ' questions answered';

            // Update submit button state - always allow submission
            const submitBtn = document.getElementById('submitBtn');
            if (answeredQuestions === totalQuestions) {
                submitBtn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                submitBtn.innerHTML = '🎉 Submit Quiz (Complete)';
            } else if (answeredQuestions > 0) {
                submitBtn.style.background = 'linear-gradient(135deg, #007bff, #0056b3)';
                submitBtn.innerHTML = '📝 Submit Quiz (' + answeredQuestions + '/' + totalQuestions + ' answered)';
            } else {
                submitBtn.style.background = 'linear-gradient(135deg, #6c757d, #495057)';
                submitBtn.innerHTML = '📝 Submit Quiz (No answers yet)';
            }
        }

        function validateForm() {
            const studentName = document.getElementById('student_name').value;

            // Clear previous error messages
            document.getElementById('nameError').style.display = 'none';

            if (!studentName) {
                document.getElementById('nameError').style.display = 'block';
                document.getElementById('student_name').focus();
                return false;
            }

            return true;
        }

        document.getElementById('quizForm').addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return;
            }

            // Show loading state
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.innerHTML = '⏳ Submitting...';
            submitBtn.disabled = true;
        });

        // Add smooth scrolling for better UX
        document.querySelectorAll('.question-card').forEach((card, index) => {
            card.addEventListener('click', function() {
                this.style.transform = 'translateY(-1px)';
                setTimeout(() => {
                    this.style.transform = 'translateY(0)';
                }, 200);
            });
        });

        // Initialize progress on page load
        updateProgress();

        // Auto-save functionality (optional)
        let autoSaveTimer;
        document.querySelectorAll('input[type="checkbox"]').forEach(input => {
            input.addEventListener('change', function() {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(() => {
                    // Could implement auto-save here if needed
                    console.log('Auto-save triggered');
                }, 2000);
            });
        });

        // Quiz status checking
        setInterval(function() {
            fetch(window.location.href + '&check=1')
                .then(response => {
                    if (response.status === 403) {
                        window.location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error checking quiz status:', error);
                });
        }, 5000);

        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && e.target.type === 'checkbox') {
                e.target.checked = !e.target.checked;
                updateProgress();
            }
        });

        // Add visual feedback for form interactions
        document.getElementById('student_name').addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        document.getElementById('student_name').addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });

        // Add change event for dropdown selection
        document.getElementById('student_name').addEventListener('change', function() {
            if (this.value) {
                document.getElementById('nameError').style.display = 'none';
            }
        });
    </script>
</body>
</html>
'''

HTML_QUIZ_RESULT = '''
<!doctype html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Quiz Results</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border-radius: 25px;
            box-shadow: 0 25px 70px rgba(0,0,0,0.2);
            padding: 50px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
        }
        .result-header {
            margin-bottom: 40px;
        }
        .result-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .celebration-emoji {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        .score-circle {
            width: 180px;
            height: 180px;
            border-radius: 50%;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            font-weight: bold;
            color: white;
            position: relative;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        .score-circle::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border-radius: 50%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: rotate 3s linear infinite;
        }
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .score-excellent {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        .score-good {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }
        .score-poor {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
        }
        .student-name {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .quiz-title {
            color: #6c757d;
            margin-bottom: 30px;
            font-size: 1.3rem;
            font-weight: 500;
        }
        .score-details {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border: 2px solid #dee2e6;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        }
        .score-details h4 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .score-details p {
            font-size: 1.1rem;
            color: #495057;
            margin: 0;
        }
        .thank-you {
            font-size: 1.2rem;
            color: #6c757d;
            font-weight: 500;
            margin-top: 30px;
        }
        .performance-badge {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            margin: 20px 0;
            font-size: 1.1rem;
        }
        .badge-excellent {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        .badge-good {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        .badge-poor {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="result-header">
            <div class="celebration-emoji">
                {% if percentage >= 80 %}🎉
                {% elif percentage >= 60 %}👏
                {% else %}💪{% endif %}
            </div>
            <h1>Quiz Completed!</h1>
        </div>

        <div class="score-circle {{score_class}}">
            {{score}}/{{total}}
        </div>

        <div class="student-name">{{student_name}}</div>
        <div class="quiz-title">{{quiz_title}}</div>

        <div class="performance-badge
            {% if percentage >= 80 %}badge-excellent
            {% elif percentage >= 60 %}badge-good
            {% else %}badge-poor{% endif %}">
            {% if percentage >= 80 %}🌟 Excellent Performance!
            {% elif percentage >= 60 %}✨ Good Job!
            {% else %}🚀 Keep Practicing!{% endif %}
        </div>

        <div class="score-details">
            <h4>Your Score: {{percentage}}%</h4>
            <p>You answered {{score}} out of {{total}} questions correctly.</p>
            {% if percentage >= 80 %}
            <p style="color: #28a745; font-weight: 600; margin-top: 10px;">Outstanding work! You've mastered this topic! 🎯</p>
            {% elif percentage >= 60 %}
            <p style="color: #ffc107; font-weight: 600; margin-top: 10px;">Great effort! You're on the right track! 📈</p>
            {% else %}
            <p style="color: #dc3545; font-weight: 600; margin-top: 10px;">Don't give up! Practice makes perfect! 💪</p>
            {% endif %}
        </div>

        <p class="thank-you">Thank you for taking the quiz! 🙏</p>
    </div>
</body>
</html>
'''

@app.route('/quiz')
def quiz_page():
    if not is_quiz_server_running():
        return render_template_string(HTML_QUIZ_FORM, quiz_closed=True), 403

    quiz_id = request.args.get('quiz_id')

    if request.args.get('check') == '1':
        return "", 200

    if not quiz_id:
        return "Quiz not found", 404

    try:
        quiz_id = int(quiz_id)
        quiz = get_quiz_with_questions(quiz_id)

        if not quiz:
            return "Quiz not found", 404

        # Get enrolled students from the database
        from facial_recognition_system.database import get_existing_records
        students_data = get_existing_records()
        student_names = sorted(students_data.keys()) if students_data else []

        return render_template_string(
            HTML_QUIZ_FORM,
            quiz_id=quiz_id,
            quiz_title=quiz['title'],
            quiz_description=quiz.get('description', ''),
            questions=quiz['questions'],
            question_count=len(quiz['questions']),
            quiz_closed=False,
            student_names=student_names
        )
    except Exception as e:
        return "Error loading quiz", 500

@app.route('/quiz', methods=['POST'])
def submit_quiz_page():
    if not is_quiz_server_running():
        return "Quiz submission closed", 403

    try:
        quiz_id = int(request.form.get('quiz_id'))
        student_name = request.form.get('student_name', '').strip()

        if not student_name:
            return "Student name is required", 400

        answers = {}
        # Use getlist to handle multiple values for the same checkbox name
        for key in request.form.keys():
            if key.startswith('question_'):
                question_id = key.replace('question_', '')
                values = request.form.getlist(key)
                answers[question_id] = values

        result = submit_quiz(quiz_id, student_name, answers)

        if not result:
            return "Error submitting quiz", 500

        score = result['score']
        total = result['total']
        percentage = round((score / total) * 100) if total > 0 else 0

        if percentage >= 80:
            score_class = 'score-excellent'
        elif percentage >= 60:
            score_class = 'score-good'
        else:
            score_class = 'score-poor'

        quiz = get_quiz_with_questions(quiz_id)
        quiz_title = quiz['title'] if quiz else 'Quiz'

        return render_template_string(
            HTML_QUIZ_RESULT,
            student_name=student_name,
            quiz_title=quiz_title,
            score=score,
            total=total,
            percentage=percentage,
            score_class=score_class
        )
    except Exception as e:
        return "Error submitting quiz", 500

def run_server(host=None, port=None):
    from facial_recognition_system.config import Config
    if host is None:
        host = Config.HOST
    if port is None:
        port = Config.QUIZ_PORT if hasattr(Config, 'QUIZ_PORT') else 5001


    if not is_quiz_server_running():
        import gui.utils.qr_code
        gui.utils.qr_code._quiz_server_running = True

    import logging
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    try:
        app.run(host=host, port=port, threaded=True)
    except Exception:
        raise

if __name__ == '__main__':
    run_server()
