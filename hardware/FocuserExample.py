# encoding: UTF-8
'''
    Arducam programmable zoom-lens controller.

    Copyright (c) 2019-4 Arducam <http://www.arducam.com>.

    Permission is hereby granted, free of charge, to any person obtaining a copy
    of this software and associated documentation files (the "Software"), to deal
    in the Software without restriction, including without limitation the rights
    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
    copies of the Software, and to permit persons to whom the Software is
    furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included in all
    copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
    IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
    DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
    OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE
    OR OTHER DEALINGS IN THE SOFTWARE.
'''

import cv2  # sudo apt-get install python-opencv
import numpy as py
import os
import sys
import time
import argparse
import curses

from .JetsonCamera import Camera
from .Focuser import Focuser
# from AutoFocus import AutoFocus

global image_count
image_count = 0

# Rendering status bar
def RenderStatusBar(stdscr):
    height, width = stdscr.getmaxyx()
    statusbarstr = "Press 'q' to exit"
    stdscr.attron(curses.color_pair(3))
    stdscr.addstr(height - 1, 0, statusbarstr)
    stdscr.addstr(height - 1, len(statusbarstr), " " * (width - len(statusbarstr) - 1))
    stdscr.attroff(curses.color_pair(3))

# Rendering description
def RenderDescription(stdscr):
    focus_desc = "Focus    : Up-Down Arrow"
    snapshot_desc = "Snapshot : 'c' Key"

    desc_y = 1
    stdscr.addstr(desc_y + 1, 0, focus_desc, curses.color_pair(1))
    stdscr.addstr(desc_y + 2, 0, snapshot_desc, curses.color_pair(1))

# Rendering middle text
def RenderMiddleText(stdscr, k, focuser):
    height, width = stdscr.getmaxyx()
    title = "Arducam Controller"[:width - 1]
    keystr = "Last key pressed: {}".format(k)[:width - 1]
    focus_value = "Focus    : {}".format(focuser.get(Focuser.OPT_FOCUS))[:width - 1]

    if k == 0:
        keystr = "No key press detected..."[:width - 1]

    start_x_title = int((width // 2) - (len(title) // 2) - len(title) % 2)
    start_x_keystr = int((width // 2) - (len(keystr) // 2) - len(keystr) % 2)
    start_x_device_info = int((width // 2) - (len("Focus    : 00000") // 2) - len("Focus    : 00000") % 2)
    start_y = int((height // 2) - 6)

    stdscr.attron(curses.color_pair(2))
    stdscr.attron(curses.A_BOLD)
    stdscr.addstr(start_y, start_x_title, title)
    stdscr.attroff(curses.color_pair(2))
    stdscr.attroff(curses.A_BOLD)

    stdscr.addstr(start_y + 5, start_x_keystr, keystr)
    stdscr.addstr(start_y + 6, start_x_device_info, focus_value)

def parseKey(k, focuser, auto_focus, camera):
    global image_count
    focus_step = 50
    if k == ord('r'):
        focuser.reset(Focuser.OPT_FOCUS)
    elif k == curses.KEY_UP:
        focuser.set(Focuser.OPT_FOCUS, focuser.get(Focuser.OPT_FOCUS) + focus_step)
    elif k == curses.KEY_DOWN:
        focuser.set(Focuser.OPT_FOCUS, focuser.get(Focuser.OPT_FOCUS) - focus_step)
    elif k == ord('c'):
        cv2.imwrite(f"image{image_count}.jpg", camera.getFrame())
        image_count += 1

def draw_menu(stdscr, camera, i2c_bus):
    focuser = Focuser(i2c_bus)
    auto_focus = None

    k = 0

    stdscr.clear()
    stdscr.refresh()
    curses.start_color()
    curses.init_pair(1, curses.COLOR_CYAN, curses.COLOR_BLACK)
    curses.init_pair(2, curses.COLOR_RED, curses.COLOR_BLACK)
    curses.init_pair(3, curses.COLOR_BLACK, curses.COLOR_WHITE)

    while (k != ord('q')):
        stdscr.clear()
        curses.flushinp()
        height, width = stdscr.getmaxyx()

        parseKey(k, focuser, auto_focus, camera)

        whstr = f"Width: {width}, Height: {height}"
        stdscr.addstr(0, 0, whstr, curses.color_pair(1))

        RenderDescription(stdscr)
        RenderStatusBar(stdscr)
        RenderMiddleText(stdscr, k, focuser)
        stdscr.refresh()

        k = stdscr.getch()

def parse_cmdline():
    parser = argparse.ArgumentParser(description='Arducam Controller.')
    parser.add_argument('-i', '--i2c-bus', type=int, required=False,
                        help='Set i2c bus, e.g., 6, 7, 8, 9, or 10.')
    return parser.parse_args()

def main():
    args = parse_cmdline()
    # Set default i2c bus if not provided
    i2c_bus = args.i2c_bus if args.i2c_bus is not None else 7  # <--- DEFAULT VALUE HERE

    print(f"Using i2c bus: {i2c_bus}")

    camera = Camera()
    camera.start_preview()

    try:
        curses.wrapper(draw_menu, camera, i2c_bus)
    finally:
        camera.stop_preview()
        camera.close()

if __name__ == "__main__":
    main()
