#!/usr/bin/env python3
"""
Camera Diagnostic Tool for Jetson Nano
This script helps diagnose camera issues and test different camera configurations.
"""

import cv2
import subprocess
import os
import sys

def run_command(command):
    """Run a shell command and return the output."""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return -1, "", str(e)

def check_system_info():
    """Check basic system information."""
    print("=" * 60)
    print("SYSTEM INFORMATION")
    print("=" * 60)

    # Check if running on Jetson
    ret, stdout, stderr = run_command("cat /etc/nv_tegra_release 2>/dev/null || echo 'Not a Jetson device'")
    print(f"Jetson Info: {stdout.strip()}")

    # Check kernel version
    ret, stdout, stderr = run_command("uname -r")
    print(f"Kernel: {stdout.strip()}")

    # Check OpenCV version
    print(f"OpenCV Version: {cv2.__version__}")

    print()

def check_video_devices():
    """Check available video devices."""
    print("=" * 60)
    print("VIDEO DEVICES")
    print("=" * 60)

    # List video devices
    ret, stdout, stderr = run_command("ls -la /dev/video*")
    if ret == 0:
        print("Available video devices:")
        print(stdout)
    else:
        print("No video devices found or error accessing /dev/video*")

    # Check for CSI camera
    ret, stdout, stderr = run_command("dmesg | grep -i camera")
    if stdout.strip():
        print("\nCamera-related kernel messages:")
        print(stdout)

    print()

def check_gstreamer():
    """Check GStreamer installation and plugins."""
    print("=" * 60)
    print("GSTREAMER CHECK")
    print("=" * 60)

    # Check GStreamer version
    ret, stdout, stderr = run_command("gst-launch-1.0 --version")
    if ret == 0:
        print(f"GStreamer: {stdout.strip()}")
    else:
        print("GStreamer not found or not working")

    # Check for nvarguscamerasrc plugin
    ret, stdout, stderr = run_command("gst-inspect-1.0 nvarguscamerasrc")
    if ret == 0:
        print("✅ nvarguscamerasrc plugin found")
    else:
        print("❌ nvarguscamerasrc plugin not found")
        print("This is required for CSI cameras on Jetson")

    print()

def test_csi_camera():
    """Test CSI camera with different configurations."""
    print("=" * 60)
    print("CSI CAMERA TEST")
    print("=" * 60)

    # Test basic GStreamer pipeline
    pipeline = "nvarguscamerasrc ! video/x-raw(memory:NVMM), width=640, height=480, format=NV12, framerate=30/1 ! nvvidconv ! video/x-raw, format=BGRx ! videoconvert ! video/x-raw, format=BGR ! appsink"

    print("Testing CSI camera with GStreamer pipeline...")
    try:
        cap = cv2.VideoCapture(pipeline, cv2.CAP_GSTREAMER)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print("✅ CSI camera working with GStreamer")
                print(f"Frame shape: {frame.shape}")
            else:
                print("❌ CSI camera opened but cannot read frames")
            cap.release()
        else:
            print("❌ Cannot open CSI camera with GStreamer")
    except Exception as e:
        print(f"❌ CSI camera test failed: {e}")

    print()

def test_usb_cameras():
    """Test USB cameras."""
    print("=" * 60)
    print("USB CAMERA TEST")
    print("=" * 60)

    working_cameras = []

    for i in range(5):  # Test indices 0-4
        try:
            print(f"Testing USB camera index {i}...")
            cap = cv2.VideoCapture(i)

            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    print(f"✅ USB camera {i} working - Frame shape: {frame.shape}")
                    working_cameras.append(i)
                else:
                    print(f"⚠️ USB camera {i} opened but cannot read frames")
                cap.release()
            else:
                print(f"❌ USB camera {i} failed to open")

        except Exception as e:
            print(f"❌ USB camera {i} error: {e}")

    if working_cameras:
        print(f"\n✅ Working USB cameras found at indices: {working_cameras}")
    else:
        print("\n❌ No working USB cameras found")

    print()

def test_jetson_camera_class():
    """Test the JetsonCamera class."""
    print("=" * 60)
    print("JETSON CAMERA CLASS TEST")
    print("=" * 60)

    try:
        # Import JetsonCamera from current hardware directory
        from .JetsonCamera import Camera as JetsonCamera

        print("Testing JetsonCamera class...")
        camera = JetsonCamera(width=640, height=360)

        # Try to get a frame
        frame = camera.getFrame(timeout=3000)  # 3 second timeout
        if frame is not None:
            print(f"✅ JetsonCamera class working - Frame shape: {frame.shape}")
        else:
            print("❌ JetsonCamera class initialized but cannot get frames")

        camera.close()

    except Exception as e:
        print(f"❌ JetsonCamera class test failed: {e}")

    print()

def main():
    """Run all diagnostic tests."""
    print("JETSON NANO CAMERA DIAGNOSTIC TOOL")
    print("This tool will help diagnose camera issues on your Jetson Nano")
    print()

    check_system_info()
    check_video_devices()
    check_gstreamer()
    test_csi_camera()
    test_usb_cameras()
    test_jetson_camera_class()

    print("=" * 60)
    print("DIAGNOSTIC COMPLETE")
    print("=" * 60)
    print("If you see errors above, here are some common solutions:")
    print()
    print("1. For CSI camera issues:")
    print("   - Make sure CSI camera is properly connected")
    print("   - Check camera cable connections")
    print("   - Try: sudo systemctl restart nvargus-daemon")
    print()
    print("2. For USB camera issues:")
    print("   - Make sure USB camera is connected")
    print("   - Try different USB ports")
    print("   - Check: lsusb | grep -i camera")
    print()
    print("3. For permission issues:")
    print("   - Try running with sudo")
    print("   - Add user to video group: sudo usermod -a -G video $USER")

if __name__ == "__main__":
    main()
