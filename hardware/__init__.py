"""
Hardware module for Teacher Assistant

This module contains camera and hardware-related components:
- JetsonCamera: CSI camera interface for Jetson Nano
- Focuser: Camera focus control for Arducam lenses
- camera_diagnostic: Diagnostic tools for camera troubleshooting
"""

# Import real camera components
from .JetsonCamera import Camera as JetsonCamera
from .Focuser import Focuser

# Always use the real Jetson camera
Camera = JetsonCamera

__all__ = ['Camera', 'JetsonCamera', 'Focuser']
