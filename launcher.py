#!/usr/bin/env python3
"""
Teacher Assistant - Cross-Platform Launcher
This script automatically detects the platform and runs the application
with the appropriate virtual environment activation.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def print_banner():
    """Print application banner."""
    print("=" * 50)
    print("    Teacher Assistant - Cross-Platform Launcher")
    print("=" * 50)
    print()


def check_requirements():
    """Check if required files exist."""
    script_dir = Path(__file__).parent
    
    # Check for main.py
    main_py = script_dir / "main.py"
    if not main_py.exists():
        print("ERROR: main.py not found!")
        print("Please make sure you're running this script from the project root directory.")
        return False
    
    # Check for virtual environment
    venv_dir = script_dir / ".venv"
    if not venv_dir.exists():
        print("ERROR: Virtual environment (.venv) not found!")
        print()
        print("To set up the project, please run one of the following:")
        print()
        if platform.system() == "Windows":
            print("Using uv (recommended):")
            print("  uv sync")
            print()
            print("Using standard Python:")
            print("  python -m venv .venv")
            print("  .venv\\Scripts\\activate")
            print("  pip install -e .")
        else:
            print("Using uv (recommended):")
            print("  uv sync")
            print()
            print("Using standard Python:")
            print("  python3 -m venv .venv")
            print("  source .venv/bin/activate")
            print("  pip install -e .")
        return False
    
    return True


def get_python_executable():
    """Get the Python executable path for the virtual environment."""
    script_dir = Path(__file__).parent
    venv_dir = script_dir / ".venv"
    
    if platform.system() == "Windows":
        python_exe = venv_dir / "Scripts" / "python.exe"
    else:
        python_exe = venv_dir / "bin" / "python"
    
    if python_exe.exists():
        return str(python_exe)
    
    # Fallback to python3 or python
    for python_name in ["python3", "python"]:
        python_exe = venv_dir / ("Scripts" if platform.system() == "Windows" else "bin") / python_name
        if python_exe.exists():
            return str(python_exe)
    
    return None


def run_application():
    """Run the Teacher Assistant application."""
    script_dir = Path(__file__).parent
    main_py = script_dir / "main.py"
    
    # Get Python executable
    python_exe = get_python_executable()
    if not python_exe:
        print("ERROR: Could not find Python executable in virtual environment!")
        return False
    
    print(f"Using Python: {python_exe}")
    print(f"Running: {main_py}")
    print()
    print("Starting Teacher Assistant application...")
    print("-" * 50)
    
    try:
        # Change to script directory
        os.chdir(script_dir)
        
        # Run the application
        result = subprocess.run([python_exe, str(main_py)], 
                              cwd=script_dir,
                              check=False)
        
        print("-" * 50)
        if result.returncode == 0:
            print("Application closed normally.")
        else:
            print(f"Application exited with error code: {result.returncode}")
        
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user.")
        return True
    except Exception as e:
        print(f"ERROR: Failed to run application: {e}")
        return False


def main():
    """Main launcher function."""
    print_banner()
    
    # Check requirements
    if not check_requirements():
        input("\nPress Enter to exit...")
        sys.exit(1)
    
    # Run application
    success = run_application()
    
    print()
    if not success:
        print("=" * 50)
        print("   Application encountered an error")
        print("=" * 50)
    
    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()
