@echo off
REM Teacher Assistant - Windows Launcher
REM This script activates the virtual environment and runs the application

echo ========================================
echo    Teacher Assistant - Starting...
echo ========================================
echo.

REM Change to the script's directory
cd /d "%~dp0"

REM Check if virtual environment exists
if not exist ".venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found!
    echo Please make sure the .venv folder exists in the project directory.
    echo.
    echo To create a virtual environment, run:
    echo   python -m venv .venv
    echo   .venv\Scripts\activate
    echo   pip install -r requirements.txt
    echo.
    pause
    exit /b 1
)

REM Check if main.py exists
if not exist "main.py" (
    echo ERROR: main.py not found!
    echo Please make sure you're running this script from the project root directory.
    echo.
    pause
    exit /b 1
)

echo Activating virtual environment...
call .venv\Scripts\activate.bat

REM Check if activation was successful
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment!
    echo.
    pause
    exit /b 1
)

echo Virtual environment activated successfully.
echo.

echo Starting Teacher Assistant application...
echo.

REM Run the main application
python main.py

REM Check if the application exited with an error
if errorlevel 1 (
    echo.
    echo ========================================
    echo    Application exited with an error
    echo ========================================
    echo.
    echo Press any key to close this window...
    pause >nul
) else (
    echo.
    echo ========================================
    echo    Application closed normally
    echo ========================================
    echo.
)

REM Deactivate virtual environment
deactivate

echo.
echo Press any key to close this window...
pause >nul
