"""
Teacher management view for admin.
"""
import flet as ft
from gui.components.admin_layout import create_admin_page_layout
from gui.components.dialogs import create_confirmation_dialog, create_form_dialog, show_dialog, close_dialog
from gui.services.auth_service import AuthService
from gui.config.constants import ROUTE_LOGIN, ROUTE_ADMIN_DASHBOARD, ICON_ADD
from gui.config.language import get_text

def create_admin_teachers_view(page: ft.Page):
    """Create the teacher management view."""
    auth_service = AuthService()
    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/teachers", controls=[])

    # Get initial teachers data with their teaching information
    teachers = auth_service.get_all_teachers()

    def get_teacher_data(teacher_id):
        """Get comprehensive data for a teacher."""
        try:
            import sqlite3
            from facial_recognition_system.config import Config

            with sqlite3.connect(Config.get_db_path()) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Get classes taught by teacher
                cursor.execute("""
                    SELECT id, name, description, created_at
                    FROM classes
                    WHERE teacher_id = ?
                    ORDER BY name
                """, (teacher_id,))
                classes = [dict(row) for row in cursor.fetchall()]

                # Get subjects taught by teacher, grouped by class
                cursor.execute("""
                    SELECT m.id, m.name, m.description, m.created_at,
                           c.name as class_name, c.id as class_id
                    FROM matieres m
                    LEFT JOIN classes c ON m.class_id = c.id
                    WHERE m.teacher_id = ?
                    ORDER BY c.name, m.name
                """, (teacher_id,))
                subjects = [dict(row) for row in cursor.fetchall()]

                # Get quizzes created by teacher
                cursor.execute("""
                    SELECT q.id, q.title, q.description, q.created_at,
                           c.name as class_name, m.name as subject_name
                    FROM quiz q
                    LEFT JOIN classes c ON q.class_id = c.id
                    LEFT JOIN matieres m ON q.subject_id = m.id
                    WHERE q.teacher_id = ?
                    ORDER BY q.created_at DESC
                """, (teacher_id,))
                quizzes = [dict(row) for row in cursor.fetchall()]

                return {
                    'classes': classes,
                    'subjects': subjects,
                    'quizzes': quizzes
                }

        except Exception as e:
            print(f"❌ Failed to get teacher data: {e}")
            return {'classes': [], 'subjects': [], 'quizzes': []}

    # Filter state
    filter_text = ft.Ref[ft.TextField]()
    filtered_teachers = teachers.copy()

    def filter_teachers(search_text=""):
        """Filter teachers based on search text."""
        nonlocal filtered_teachers
        if not search_text:
            filtered_teachers = teachers.copy()
        else:
            search_lower = search_text.lower()
            filtered_teachers = [
                teacher for teacher in teachers
                if (search_lower in teacher['full_name'].lower() or
                    search_lower in teacher['username'].lower() or
                    search_lower in (teacher['email'] or '').lower())
            ]
        refresh_teachers()

    def on_filter_change(e):
        """Handle filter text change."""
        filter_teachers(e.control.value)

    # Filter section
    filter_section = ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.SEARCH, size=24, color=ft.Colors.BLUE_600),
                ft.Text(
                    "Rechercher des Enseignants",
                    size=18,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_900
                )
            ], spacing=12, alignment=ft.MainAxisAlignment.CENTER),
            ft.TextField(
                ref=filter_text,
                hint_text="Rechercher par nom, nom d'utilisateur ou email...",
                border_radius=12,
                content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
                border_color=ft.Colors.BLUE_200,
                focused_border_color=ft.Colors.BLUE_600,
                prefix_icon=ft.Icons.SEARCH,
                on_change=on_filter_change,
                width=page.width*0.8 if is_mobile else 500
            )
        ], spacing=12, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(16),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=8,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLUE_600),
            offset=ft.Offset(0, 4)
        ),
        border=ft.border.all(1, ft.Colors.BLUE_100)
    )

    # Modern welcome section with gradient background and French text
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(
                    ft.Icons.PEOPLE,
                    size=36,
                    color=ft.Colors.WHITE
                ),
                ft.Text(
                    "Gestion des Enseignants",
                    size=32,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.WHITE,
                    text_align=ft.TextAlign.CENTER
                )
            ], alignment=ft.MainAxisAlignment.CENTER, spacing=12),
            ft.Text(
                f"{len(teachers)} enseignant{'s' if len(teachers) > 1 else ''} dans le système",
                size=18,
                color=ft.Colors.WHITE70,
                text_align=ft.TextAlign.CENTER,
                weight=ft.FontWeight.W_400
            )
        ],
        spacing=12,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(28),
        margin=ft.margin.only(bottom=24, top=0),
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.BLUE_700, ft.Colors.INDIGO_600, ft.Colors.PURPLE_600]
        ),
        border_radius=ft.border_radius.all(24),
        alignment=ft.alignment.center,
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=20,
            color=ft.Colors.with_opacity(0.3, ft.Colors.BLUE_600),
            offset=ft.Offset(0, 8)
        )
    )

    # Create a modern column for teacher cards
    teachers_container = ft.Column(
        spacing=16,
        width=page.width*0.9 if is_mobile else 600,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER
    )

    # Store reference to containers for dynamic updates
    page._teachers_container = teachers_container

    def refresh_teachers():
        """Refresh the teachers list."""
        nonlocal teachers, filtered_teachers
        teachers_container.controls.clear()
        fresh_teachers = auth_service.get_all_teachers()
        teachers = fresh_teachers

        # Apply current filter
        current_filter = filter_text.current.value if filter_text.current else ""
        filter_teachers(current_filter)

        # Update welcome section with teacher count
        welcome_section.content.controls[1].value = f"{len(filtered_teachers)} enseignant{'s' if len(filtered_teachers) > 1 else ''} affiché{'s' if len(filtered_teachers) > 1 else ''} sur {len(teachers)}"

        # Update no teachers message visibility
        no_teachers_message.visible = len(filtered_teachers) == 0

        if len(filtered_teachers) == 0:
            page.update()
            return

        # Create teacher cards
        for teacher in filtered_teachers:
            teacher_card = create_teacher_card(teacher)
            teachers_container.controls.append(teacher_card)

        page.update()

    def create_teacher_card(teacher: dict):
        """Create an enhanced teacher card with detailed teaching information."""

        # Get teacher's teaching data
        teacher_data = get_teacher_data(teacher['id'])

        def create_delete_handler(teacher_data):
            def delete_teacher(e):
                e.control.parent.parent.parent.parent.parent.update()  # Stop event propagation
                def confirm_delete():
                    if auth_service.delete_teacher(teacher_data['id']):
                        refresh_teachers()
                        page.app_state.show_success("Enseignant supprimé avec succès")

                dialog = create_confirmation_dialog(
                    page,
                    "Confirmer la Suppression",
                    f"Êtes-vous sûr de vouloir supprimer l'enseignant '{teacher_data['full_name']}'?",
                    confirm_delete,
                    confirm_text="Supprimer",
                    is_destructive=True
                )
                show_dialog(page, dialog)
            return delete_teacher

        # Group subjects by class
        subjects_by_class = {}
        for subject in teacher_data['subjects']:
            class_name = subject['class_name'] or 'Sans classe'
            if class_name not in subjects_by_class:
                subjects_by_class[class_name] = []
            subjects_by_class[class_name].append(subject)

        # Create subjects display grouped by class
        subjects_display = []
        for class_name, subjects in subjects_by_class.items():
            subjects_display.append(
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            f"📚 {class_name}:",
                            size=13,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.PURPLE_700
                        ),
                        ft.Column([
                            ft.Text(
                                f"  • {subject['name']}",
                                size=12,
                                color=ft.Colors.GREY_700
                            ) for subject in subjects
                        ], spacing=2)
                    ], spacing=4),
                    margin=ft.margin.only(bottom=8)
                )
            )

        # Create expandable sections
        expanded_content = ft.Column([
            # Classes section
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.SCHOOL, size=16, color=ft.Colors.GREEN_600),
                        ft.Text(
                            f"Classes ({len(teacher_data['classes'])})",
                            size=14,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREEN_700
                        )
                    ], spacing=8),
                    ft.Column([
                        ft.Text(
                            f"• {cls['name']}" + (f" - {cls['description']}" if cls['description'] else ""),
                            size=12,
                            color=ft.Colors.GREY_700
                        ) for cls in teacher_data['classes']
                    ], spacing=2) if teacher_data['classes'] else [
                        ft.Text("Aucune classe assignée", size=12, color=ft.Colors.GREY_500, italic=True)
                    ]
                ], spacing=6),
                padding=ft.padding.all(12),
                bgcolor=ft.Colors.GREEN_50,
                border_radius=8,
                margin=ft.margin.only(bottom=8)
            ),

            # Subjects section (grouped by class)
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.BOOK, size=16, color=ft.Colors.PURPLE_600),
                        ft.Text(
                            f"Matières ({len(teacher_data['subjects'])})",
                            size=14,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.PURPLE_700
                        )
                    ], spacing=8),
                    ft.Column(
                        subjects_display if subjects_display else [
                            ft.Text("Aucune matière assignée", size=12, color=ft.Colors.GREY_500, italic=True)
                        ],
                        spacing=4
                    )
                ], spacing=6),
                padding=ft.padding.all(12),
                bgcolor=ft.Colors.PURPLE_50,
                border_radius=8,
                margin=ft.margin.only(bottom=8)
            ),

            # Quizzes section
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.QUIZ, size=16, color=ft.Colors.ORANGE_600),
                        ft.Text(
                            f"Quiz ({len(teacher_data['quizzes'])})",
                            size=14,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.ORANGE_700
                        )
                    ], spacing=8),
                    ft.Column([
                        ft.Text(
                            f"• {quiz['title']}" +
                            (f" ({quiz['class_name']} - {quiz['subject_name']})" if quiz['class_name'] and quiz['subject_name'] else ""),
                            size=12,
                            color=ft.Colors.GREY_700
                        ) for quiz in teacher_data['quizzes'][:5]  # Show only first 5 quizzes
                    ], spacing=2) if teacher_data['quizzes'] else [
                        ft.Text("Aucun quiz créé", size=12, color=ft.Colors.GREY_500, italic=True)
                    ]
                ], spacing=6),
                padding=ft.padding.all(12),
                bgcolor=ft.Colors.ORANGE_50,
                border_radius=8
            )
        ], spacing=8)

        # Expandable card state
        is_expanded = ft.Ref[bool]()
        is_expanded.current = False

        def toggle_expand(_):
            is_expanded.current = not is_expanded.current
            expanded_content.visible = is_expanded.current
            expand_button.icon = ft.Icons.EXPAND_LESS if is_expanded.current else ft.Icons.EXPAND_MORE
            expand_button.tooltip = "Réduire" if is_expanded.current else "Voir détails"
            page.update()

        expand_button = ft.IconButton(
            icon=ft.Icons.EXPAND_MORE,
            icon_color=ft.Colors.BLUE_600,
            tooltip="Voir détails",
            bgcolor=ft.Colors.BLUE_50,
            icon_size=20,
            on_click=toggle_expand
        )

        # Initially hide expanded content
        expanded_content.visible = False

        return ft.Container(
            content=ft.Column([
                # Header with avatar and name
                ft.Row([
                    ft.Container(
                        content=ft.Icon(
                            ft.Icons.PERSON,
                            size=24,
                            color=ft.Colors.BLUE_600
                        ),
                        width=48,
                        height=48,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=24,
                        alignment=ft.alignment.center
                    ),
                    ft.Column([
                        ft.Text(
                            teacher['full_name'],
                            size=18,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.BLUE_900
                        ),
                        ft.Text(
                            f"@{teacher['username']}",
                            size=14,
                            color=ft.Colors.GREY_600,
                            weight=ft.FontWeight.W_500
                        )
                    ], spacing=4, expand=True),
                    ft.Row([
                        expand_button,
                        ft.Container(
                            content=ft.IconButton(
                                icon=ft.Icons.DELETE_OUTLINE,
                                icon_color=ft.Colors.RED_500,
                                tooltip="Supprimer l'Enseignant",
                                bgcolor=ft.Colors.RED_50,
                                icon_size=20,
                                on_click=create_delete_handler(teacher)
                            ),
                            shadow=ft.BoxShadow(
                                spread_radius=0,
                                blur_radius=4,
                                color=ft.Colors.with_opacity(0.1, ft.Colors.RED_300),
                                offset=ft.Offset(0, 2)
                            ),
                            border_radius=20
                        )
                    ], spacing=8)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN, spacing=12),

                ft.Divider(height=1, color=ft.Colors.GREY_200),

                # Basic details section
                ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.EMAIL, size=16, color=ft.Colors.GREY_600),
                        ft.Text(f"Email: {teacher['email'] or 'Non fourni'}", size=14, color=ft.Colors.GREY_700)
                    ], spacing=8),
                    ft.Row([
                        ft.Icon(ft.Icons.CALENDAR_TODAY, size=16, color=ft.Colors.GREY_600),
                        ft.Text(f"Créé le: {teacher['created_at'][:10]}", size=14, color=ft.Colors.GREY_700)
                    ], spacing=8),
                    ft.Row([
                        ft.Icon(ft.Icons.ANALYTICS, size=16, color=ft.Colors.GREY_600),
                        ft.Text(
                            f"Activité: {len(teacher_data['classes'])} classes, {len(teacher_data['subjects'])} matières, {len(teacher_data['quizzes'])} quiz",
                            size=14,
                            color=ft.Colors.GREY_700
                        )
                    ], spacing=8)
                ], spacing=6),

                # Expandable detailed content
                expanded_content
            ], spacing=16),
            padding=ft.padding.all(24),
            bgcolor=ft.Colors.WHITE,
            border_radius=ft.border_radius.all(20),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=12,
                color=ft.Colors.with_opacity(0.08, ft.Colors.BLACK),
                offset=ft.Offset(0, 4)
            ),
            margin=ft.margin.all(8),
            ink=True,
            border=ft.border.all(1, ft.Colors.BLUE_100),
            animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT)
        )

    def show_add_teacher_dialog(_):
        """Show add teacher dialog."""
        username_field = ft.TextField(
            label="Nom d'utilisateur",
            hint_text="Entrez le nom d'utilisateur",
            border_radius=12,
            content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
            border_color=ft.Colors.BLUE_200,
            focused_border_color=ft.Colors.BLUE_600,
        )
        password_field = ft.TextField(
            label="Mot de passe",
            hint_text="Entrez le mot de passe",
            password=True,
            border_radius=12,
            content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
            border_color=ft.Colors.BLUE_200,
            focused_border_color=ft.Colors.BLUE_600,
        )
        full_name_field = ft.TextField(
            label="Nom complet",
            hint_text="Entrez le nom complet",
            border_radius=12,
            content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
            border_color=ft.Colors.BLUE_200,
            focused_border_color=ft.Colors.BLUE_600,
        )
        email_field = ft.TextField(
            label="Email (Optionnel)",
            hint_text="Entrez l'email (optionnel)",
            border_radius=12,
            content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
            border_color=ft.Colors.BLUE_200,
            focused_border_color=ft.Colors.BLUE_600,
        )
        phone_field = ft.TextField(
            label="Téléphone (Optionnel)",
            hint_text="Entrez le téléphone (optionnel)",
            border_radius=12,
            content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
            border_color=ft.Colors.BLUE_200,
            focused_border_color=ft.Colors.BLUE_600,
        )

        def add_teacher():
            if not all([username_field.value, password_field.value, full_name_field.value]):
                username_field.error_text = "Le nom d'utilisateur est requis" if not username_field.value else ""
                password_field.error_text = "Le mot de passe est requis" if not password_field.value else ""
                full_name_field.error_text = "Le nom complet est requis" if not full_name_field.value else ""
                page.update()
                return

            success = auth_service.create_teacher(
                username=username_field.value.strip(),
                password=password_field.value.strip(),
                full_name=full_name_field.value.strip(),
                email=email_field.value.strip() or None,
                phone=phone_field.value.strip() or None
            )

            if success:
                refresh_teachers()
                page.app_state.show_success("Enseignant créé avec succès")
            else:
                page.app_state.show_error("Échec de la création de l'enseignant. Le nom d'utilisateur existe peut-être déjà.")

        form_controls = [
            username_field,
            password_field,
            full_name_field,
            email_field,
            phone_field,
        ]

        dialog = create_form_dialog(
            page,
            "Ajouter un Nouvel Enseignant",
            form_controls,
            add_teacher,
            submit_text="Ajouter l'Enseignant"
        )
        show_dialog(page, dialog)

    # Add teacher button with modern French styling
    add_teacher_button = ft.ElevatedButton(
        "Ajouter un Enseignant",
        icon=ICON_ADD,
        tooltip="Ajouter un nouvel enseignant",
        on_click=show_add_teacher_dialog,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            shape=ft.RoundedRectangleBorder(radius=16),
            padding=ft.padding.symmetric(horizontal=24, vertical=16),
            shadow_color=ft.Colors.BLUE_200,
            elevation=4,
            text_style=ft.TextStyle(size=16, weight=ft.FontWeight.W_600)
        ),
    )

    # Modern add teacher form with French text
    add_teacher_form = ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.PERSON_ADD, size=24, color=ft.Colors.BLUE_600),
                ft.Text(
                    "Ajouter un Nouvel Enseignant",
                    size=20,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_900
                )
            ], spacing=12, alignment=ft.MainAxisAlignment.CENTER),
            ft.Text(
                "Créez un nouveau compte enseignant pour votre établissement",
                size=14,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER
            ),
            add_teacher_button,
        ], spacing=16, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        width=page.width*0.9 if is_mobile else 600,
        padding=ft.padding.all(28),
        margin=ft.margin.only(bottom=24),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(20),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=12,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLUE_600),
            offset=ft.Offset(0, 4)
        ),
        border=ft.border.all(1, ft.Colors.BLUE_100)
    )

    # No teachers message with French text and better design
    no_teachers_message = ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Icon(
                    ft.Icons.PERSON_ADD,
                    size=64,
                    color=ft.Colors.BLUE_300
                ),
                width=120,
                height=120,
                bgcolor=ft.Colors.BLUE_50,
                border_radius=60,
                alignment=ft.alignment.center
            ),
            ft.Text(
                "Aucun enseignant pour le moment",
                size=22,
                color=ft.Colors.BLUE_900,
                text_align=ft.TextAlign.CENTER,
                weight=ft.FontWeight.BOLD
            ),
            ft.Text(
                "Ajoutez votre premier enseignant pour commencer",
                size=16,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER
            )
        ], spacing=20, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        alignment=ft.alignment.center,
        padding=ft.padding.all(48),
        visible=len(teachers) == 0,
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(20),
        margin=ft.margin.only(bottom=24),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=8,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLUE_600),
            offset=ft.Offset(0, 4)
        ),
        border=ft.border.all(1, ft.Colors.BLUE_100)
    )

    # Create teacher cards for each existing teacher (initial load)
    for teacher in filtered_teachers:
        teacher_card = create_teacher_card(teacher)
        teachers_container.controls.append(teacher_card)

    # Store the refresh function for use in dialogs
    page._refresh_teachers = refresh_teachers

    # Create enhanced content
    content = [
        welcome_section,
        ft.Container(
            content=filter_section,
            alignment=ft.alignment.center,
        ),
        ft.Container(
            content=add_teacher_form,
            alignment=ft.alignment.center,
        ),
        no_teachers_message,
        ft.Container(
            content=teachers_container,
            alignment=ft.alignment.center,
        )
    ]

    return create_admin_page_layout(
        page,
        "Gestion des Enseignants",
        content
    )
