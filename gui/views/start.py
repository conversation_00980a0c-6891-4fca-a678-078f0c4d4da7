import flet as ft
from gui.config.constants import ROUTE_DASHBOARD
from gui.config.language import get_text

def create_start_view(page: ft.Page):
    """Create the start page with app description, logo, and creator info."""
    is_mobile = getattr(page, 'is_mobile', False)
    current_language = getattr(page, 'language', 'fr')

    # App logo/icon (using a built-in icon as placeholder for Ministry logo)
    logo_container = ft.Container(
        content=ft.Icon(
            ft.Icons.SCHOOL,
            size=80,
            color=ft.Colors.BLUE_600
        ),
        margin=ft.margin.only(bottom=20)
    )

    # Ministry of Education Tunisia text
    ministry_text = ft.Text(
        get_text("ministry_education_tunisia", current_language),
        size=16,
        weight=ft.FontWeight.W_500,
        color=ft.Colors.BLUE_800,
        text_align=ft.TextAlign.CENTER
    )

    # App title
    app_title = ft.Text(
        get_text("app_name", current_language),
        size=36,
        weight=ft.FontWeight.BOLD,
        color=ft.Colors.BLUE_900,
        text_align=ft.TextAlign.CENTER
    )

    # App description
    app_description = ft.Text(
        get_text("educational_management_system", current_language),
        size=16,
        color=ft.Colors.BLUE_GREY_200,
        text_align=ft.TextAlign.CENTER,
        width=500 if not is_mobile else page.width * 0.9
    )

    # Get started button
    get_started_button = ft.ElevatedButton(
        text=get_text("get_started", current_language),
        icon=ft.Icons.ARROW_FORWARD,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            padding=ft.padding.symmetric(horizontal=32, vertical=16),
            text_style=ft.TextStyle(
                size=18,
                weight=ft.FontWeight.W_500
            )
        ),
        on_click=lambda _: page.go(ROUTE_DASHBOARD)
    )



    # Main content container
    main_content = ft.Container(
        content=ft.Column([
            logo_container,
            ministry_text,
            ft.Container(height=20),
            app_title,
            ft.Container(height=20),
            app_description,
            ft.Container(height=40),
            get_started_button,
            ft.Container(height=60),
        ],
        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        spacing=10),
        padding=ft.padding.all(40),
        alignment=ft.alignment.center,
        expand=True
    )

    # Gradient background container
    background_container = ft.Container(
        content=main_content,
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_center,
            end=ft.alignment.bottom_center,
            colors=[ft.Colors.BLUE_50, ft.Colors.WHITE, ft.Colors.BLUE_50]
        ),
        expand=True
    )

    # Create the view
    view = ft.View(
        route="/",
        controls=[background_container],
        padding=0,
        spacing=0
    )

    return view
