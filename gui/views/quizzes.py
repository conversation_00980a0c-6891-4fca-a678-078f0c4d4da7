import flet as ft
from gui.components.layout import create_page_layout
from quiz_management.services.quiz_service import *
from gui.services.class_service import get_existing_classes
from gui.services.subject_service import get_class_subjects
from gui.components.dialogs import create_confirmation_dialog, show_dialog, close_dialog
from gui.config.constants import ICON_ADD
from gui.config.language import get_text
from gui.utils.qr_code import generate_quiz_url, create_qr_code_image, start_quiz_server, stop_quiz_server

def create_button(text, icon, on_click, color=ft.Colors.BLUE_600, bgcolor=None):
    return ft.ElevatedButton(
        text=text, icon=icon, on_click=on_click,
        style=ft.ButtonStyle(
            color=ft.Colors.WHITE if bgcolor else color,
            bgcolor=bgcolor or ft.Colors.TRANSPARENT,
            padding=ft.padding.symmetric(horizontal=16, vertical=8),
            shape=ft.RoundedRectangleBorder(radius=6)
        )
    )

def create_text_field(label, hint="", multiline=False, ref=None):
    return ft.TextField(
        label=label, hint_text=hint, multiline=multiline, ref=ref,
        border_color=ft.Colors.GREY_300, focused_border_color=ft.Colors.BLUE_600
    )

def show_snackbar(message, is_error=False):
    print(f"{'ERROR: ' if is_error else ''}{message}")

def validate_fields(fields):
    has_errors = False
    for field, error_msg in fields:
        field.error_text = None
        if not field.value or not field.value.strip() or field.value == "select_subject":
            field.error_text = error_msg
            has_errors = True
    return not has_errors

def create_quizzes_view(page: ft.Page):
    initialize_quiz_tables()
    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # Get current user and filter data by teacher
    current_user = getattr(page.app_state, 'current_user', None) if hasattr(page, 'app_state') else None
    teacher_id = current_user['id'] if current_user and current_user['role'] == 'teacher' else None

    # Get initial data for welcome section
    if current_user and current_user['role'] == 'admin':
        quizzes = get_quizzes()
    else:
        quizzes = get_quizzes(teacher_id=teacher_id)

    # Modern welcome section with gradient background
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Text(
                get_text("quizzes", current_language),
                size=28,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.WHITE,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                get_text("quizzes_count", current_language).format(count=len(quizzes)),
                size=16,
                color=ft.Colors.WHITE70,
                text_align=ft.TextAlign.CENTER
            )
        ],
        spacing=8,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20, top=0),
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.PURPLE_600, ft.Colors.PINK_600]
        ),
        border_radius=ft.border_radius.all(20),
        alignment=ft.alignment.center
    )

    # Filter controls - get classes filtered by teacher
    if current_user and current_user['role'] == 'admin':
        classes = get_existing_classes()
    else:
        classes = get_existing_classes(teacher_id)
    filter_class_dropdown = ft.Dropdown(
        hint_text=get_text("filter_by_class", current_language),
        options=[ft.dropdown.Option(key="all", text=get_text("all_classes", current_language))] +
                [ft.dropdown.Option(key=str(info['id']), text=name) for name, info in classes.items()],
        value="all",
        expand=True
    )

    filter_subject_dropdown = ft.Dropdown(
        hint_text=get_text("filter_by_subject", current_language),
        options=[ft.dropdown.Option(key="all", text=get_text("all_subjects", current_language))],
        value="all",
        expand=True,
        disabled=True
    )

    def on_filter_class_change(_):
        filter_subject_dropdown.value = "all"

        if filter_class_dropdown.value and filter_class_dropdown.value != "all":
            # Get subjects filtered by teacher
            if current_user and current_user['role'] == 'admin':
                subjects = get_class_subjects(int(filter_class_dropdown.value))
            else:
                subjects = get_class_subjects(int(filter_class_dropdown.value), teacher_id)
            filter_subject_dropdown.options = [ft.dropdown.Option(key="all", text=get_text("all_subjects", current_language))] + \
                                            [ft.dropdown.Option(key=str(s['id']), text=s['subject_name']) for s in subjects]
            filter_subject_dropdown.disabled = False
        else:
            filter_subject_dropdown.options = [ft.dropdown.Option(key="all", text=get_text("all_subjects", current_language))]
            filter_subject_dropdown.disabled = True

        load_quizzes()
        page.update()

    def on_filter_subject_change(_):
        load_quizzes()
        page.update()

    filter_class_dropdown.on_change = on_filter_class_change
    filter_subject_dropdown.on_change = on_filter_subject_change

    # Filter section
    filter_section = ft.Container(
        content=ft.Column([
            ft.Text(
                get_text("filter_quizzes", current_language),
                size=16,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.ON_SURFACE
            ),
            ft.Row([filter_class_dropdown, filter_subject_dropdown], spacing=12) if not is_mobile else
            ft.Column([filter_class_dropdown, filter_subject_dropdown], spacing=12),
        ], spacing=12),
        width=page.width*0.9 if is_mobile else 600,
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20),
        bgcolor=ft.Colors.SURFACE,
        border_radius=ft.border_radius.all(12),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=4,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 1)
        ),
    )

    title_field = ft.TextField(label=get_text("quiz_title", current_language), expand=True)
    description_field = ft.TextField(
        label=get_text("description_optional", current_language),
        hint_text=get_text("enter_quiz_description", current_language),
        multiline=True,
        min_lines=2,
        max_lines=4,
        expand=True
    )

    class_dropdown = ft.Dropdown(
        label=get_text("select_class", current_language),
        options=[ft.dropdown.Option(key=str(info['id']), text=name) for name, info in classes.items()],
        expand=True,
        width=page.width*0.9 if is_mobile else 300
    )
    subject_dropdown = ft.Dropdown(
        label=get_text("select_subject", current_language),
        hint_text=get_text("select_subject", current_language),
        options=[ft.dropdown.Option(key="select_subject", text=get_text("select_subject", current_language))],
        value="select_subject",
        expand=True,
        disabled=True
    )

    def on_class_change(_):
        subject_dropdown.value = "select_subject"
        subject_dropdown.error_text = None

        if class_dropdown.value:
            # Get subjects filtered by teacher
            if current_user and current_user['role'] == 'admin':
                subjects = get_class_subjects(int(class_dropdown.value))
            else:
                subjects = get_class_subjects(int(class_dropdown.value), teacher_id)
            subject_dropdown.options = [ft.dropdown.Option(key="select_subject", text=get_text("select_subject", current_language))] + [ft.dropdown.Option(key=str(s['id']), text=s['subject_name']) for s in subjects]
            subject_dropdown.disabled = False
        else:
            subject_dropdown.options = [ft.dropdown.Option(key="select_subject", text=get_text("select_subject", current_language))]
            subject_dropdown.disabled = True

        page.update()

    class_dropdown.on_change = on_class_change



    # Create modern add button
    add_button = ft.ElevatedButton(
        get_text("add", current_language),
        icon=ICON_ADD,
        icon_color=ft.Colors.WHITE,
        on_click=lambda _: handle_create_quiz(title_field.value, description_field.value,
                                   class_dropdown.value, subject_dropdown.value),
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.PURPLE_600,
            color=ft.Colors.WHITE,
            shape=ft.RoundedRectangleBorder(radius=12),
            padding=ft.padding.symmetric(horizontal=20, vertical=12),
            shadow_color=ft.Colors.PURPLE_200,
            elevation=2
        ),
    )

    # Modern quiz form with enhanced styling and better layout
    quiz_form = ft.Container(
        content=ft.Column([
            ft.Text(
                get_text("create_new_quiz", current_language),
                size=18,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.ON_SURFACE
            ),
            # Quiz title field (full width)
            title_field,
            # Description field (full width)
            description_field,
            # Dropdowns in a row for better organization
            ft.Row([class_dropdown, subject_dropdown], spacing=12),
            # Add button centered at the bottom
            ft.Container(
                content=add_button,
                alignment=ft.alignment.center,
                margin=ft.margin.only(top=8)
            ),
        ], spacing=16),
        width=page.width*0.9 if is_mobile else 600,
        padding=ft.padding.all(24),
        margin=ft.margin.only(bottom=20),
        bgcolor=ft.Colors.SURFACE,
        border_radius=ft.border_radius.all(16),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=8,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 2)
        ),
    )

    quizzes_list = ft.Column(
        spacing=16,
        width=page.width*0.9 if is_mobile else 600,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER
    )

    def handle_create_quiz(title, description, class_id, subject_id):
        fields = [
            (title_field, get_text("please_enter_quiz_title", current_language)),
            (class_dropdown, get_text("please_select_class", current_language)),
            (subject_dropdown, get_text("please_select_subject", current_language))
        ]

        if not validate_fields(fields):
            page.update()
            return

        try:
            # Get current teacher ID
            current_user = getattr(page.app_state, 'current_user', None) if hasattr(page, 'app_state') else None
            teacher_id = current_user['id'] if current_user and current_user['role'] == 'teacher' else None

            quiz_id = create_quiz(title.strip(), description.strip(), int(class_id), int(subject_id), teacher_id)
            if quiz_id:
                show_snackbar(f"{title} {get_text('created_successfully', current_language)}")
                for field in [title_field, description_field, class_dropdown, subject_dropdown]:
                    field.value = ""
                subject_dropdown.options = []
                subject_dropdown.disabled = True
                load_quizzes()
                show_quiz_details(quiz_id, auto_add_question=False)
            else:
                show_snackbar(get_text("failed_to_create_quiz", current_language), True)
        except Exception as ex:
            show_snackbar(f"{get_text('error_creating_quiz', current_language)}: {str(ex)}", True)
        page.update()

    def load_quizzes():
        quizzes_list.controls.clear()

        # Get filter values
        filter_class_id = None if filter_class_dropdown.value == "all" else int(filter_class_dropdown.value) if filter_class_dropdown.value else None
        filter_subject_id = None if filter_subject_dropdown.value == "all" else int(filter_subject_dropdown.value) if filter_subject_dropdown.value else None

        # Get filtered quizzes with teacher filtering
        current_user = getattr(page.app_state, 'current_user', None) if hasattr(page, 'app_state') else None
        teacher_id = current_user['id'] if current_user and current_user['role'] == 'teacher' else None

        if current_user and current_user['role'] == 'admin':
            quizzes = get_quizzes(class_id=filter_class_id, subject_id=filter_subject_id)
        else:
            quizzes = get_quizzes(class_id=filter_class_id, subject_id=filter_subject_id, teacher_id=teacher_id)

        if quizzes:
            # Group quizzes by class
            quizzes_by_class = {}
            for quiz in quizzes:
                class_id = quiz['class_id']
                if class_id not in quizzes_by_class:
                    quizzes_by_class[class_id] = []
                quizzes_by_class[class_id].append(quiz)

            # Add header for quiz list
            filter_text = ""
            if filter_class_id:
                class_name = next((name for name, info in classes.items() if info['id'] == filter_class_id), get_text("unknown_class", current_language))
                filter_text = f" in {class_name}"
                if filter_subject_id:
                    # Get subjects filtered by teacher
                    if current_user and current_user['role'] == 'admin':
                        subjects = get_class_subjects(filter_class_id)
                    else:
                        subjects = get_class_subjects(filter_class_id, teacher_id)
                    subject_name = next((s['subject_name'] for s in subjects if s['id'] == filter_subject_id), get_text("unknown_subject", current_language))
                    filter_text += f" - {subject_name}"

            quizzes_list.controls.append(
                ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.Icons.LIST_ALT, color=ft.Colors.BLUE_600),
                        ft.Text(get_text("your_quizzes", current_language).format(count=len(quizzes), filter=filter_text), weight=ft.FontWeight.BOLD, size=16),
                    ]),
                    margin=ft.margin.only(bottom=16),
                )
            )

            # Display quizzes grouped by class
            for class_id, class_quizzes in quizzes_by_class.items():
                class_name = next((name for name, info in classes.items() if info['id'] == class_id), get_text("unknown_class", current_language))

                # Add class header (only if not filtering by specific class)
                if not filter_class_id:
                    quizzes_list.controls.append(
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.SCHOOL, color=ft.Colors.GREEN_600, size=20),
                                ft.Text(f"{class_name} ({len(class_quizzes)} {get_text('quizzes_word', current_language)})",
                                       weight=ft.FontWeight.BOLD, size=14, color=ft.Colors.GREEN_700),
                            ]),
                            padding=ft.padding.symmetric(horizontal=8, vertical=4),
                            margin=ft.margin.only(top=8, bottom=8),
                            bgcolor=ft.Colors.GREEN_50,
                            border_radius=8,
                        )
                    )

                # Add quizzes for this class
                for quiz in class_quizzes:
                    subject_name = ""
                    if quiz['subject_id']:
                        # Get subjects filtered by teacher
                        if current_user and current_user['role'] == 'admin':
                            subjects = get_class_subjects(quiz['class_id'])
                        else:
                            subjects = get_class_subjects(quiz['class_id'], teacher_id)
                        subject_name = next((s['subject_name'] for s in subjects if s['id'] == quiz['subject_id']), "")

                    quiz_with_questions = get_quiz_with_questions(quiz['id'])
                    question_count = len(quiz_with_questions['questions']) if quiz_with_questions else 0

                    menu_items = [
                        ft.PopupMenuItem(text=get_text("edit", current_language), icon=ft.Icons.EDIT, on_click=lambda _, q=quiz['id']: show_quiz_details(q)),
                        ft.PopupMenuItem(text=get_text("qr_code", current_language), icon=ft.Icons.QR_CODE, on_click=lambda _, q=quiz['id'], t=quiz['title']: show_quiz_qr_code(q, t)),
                        ft.PopupMenuItem(text=get_text("results", current_language), icon=ft.Icons.ASSESSMENT, on_click=lambda _, q=quiz['id'], t=quiz['title']: show_quiz_results(q, t)),
                        ft.PopupMenuItem(text=get_text("delete_quiz", current_language), icon=ft.Icons.DELETE_OUTLINE, on_click=lambda _, q=quiz['id'], t=quiz['title']: confirm_delete_quiz(q, t)),
                    ]

                    # Show class name only if not filtering by class
                    subtitle_text = subject_name if filter_class_id else f"{class_name} - {subject_name}"

                    quiz_card = ft.Card(
                        content=ft.Container(
                            content=ft.Column([
                                ft.Row([
                                    ft.Icon(ft.Icons.QUIZ, color=ft.Colors.BLUE_600, size=24),
                                    ft.Column([
                                        ft.Text(quiz['title'], weight=ft.FontWeight.BOLD, size=16),
                                        ft.Text(subtitle_text, size=12, color=ft.Colors.GREY_600),
                                        ft.Text(get_text("questions_count", current_language).format(count=question_count), size=12, color=ft.Colors.BLUE_600),
                                    ], expand=True, spacing=2),
                                    ft.PopupMenuButton(
                                        icon=ft.Icons.MORE_VERT,
                                        items=menu_items,
                                    ),
                                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                            ]),
                            padding=ft.padding.all(16),
                            width=500,
                        ),
                        elevation=2,
                        margin=ft.margin.only(left=16 if not filter_class_id else 6, right=6, top=3, bottom=3),
                    )
                    quizzes_list.controls.append(quiz_card)
        else:
            # Add empty state
            empty_message = get_text("no_quizzes_found", current_language)
            if filter_class_id or filter_subject_id:
                empty_message = get_text("no_quizzes_match_filters", current_language)

            quizzes_list.controls.append(
                ft.Container(
                    content=ft.Column([
                        ft.Icon(ft.Icons.QUIZ, size=48, color=ft.Colors.GREY_400),
                        ft.Text(empty_message, weight=ft.FontWeight.BOLD, size=18, color=ft.Colors.GREY_600),
                        ft.Text(get_text("try_adjusting_filters", current_language), size=14, color=ft.Colors.GREY_500),
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=12),
                    padding=ft.padding.all(40),
                    alignment=ft.alignment.center,
                    border=ft.border.all(2, ft.Colors.GREY_300),
                    border_radius=12,
                    bgcolor=ft.Colors.GREY_50,
                )
            )

        page.update()

    def show_quiz_details(quiz_id, auto_add_question=False):
        quiz = get_quiz_with_questions(quiz_id)
        if not quiz:
            show_snackbar(get_text("quiz_not_found", current_language), True)
            page.update()
            return

        question_form_visible = ft.Ref[ft.Container]()
        question_field = ft.Ref[ft.TextField]()
        options_container = ft.Ref[ft.Column]()
        empty_state_visible = ft.Ref[ft.Container]()

        def create_option_field(text="", is_correct=False, option_id=None):
            container = ft.Container(
                content=ft.Row([
                    ft.Checkbox(value=is_correct),
                    create_text_field("", get_text("enter_option_text", current_language), ref=None),
                    ft.IconButton(icon=ft.Icons.DELETE_OUTLINE, icon_color=ft.Colors.RED_600, tooltip=get_text("remove_option", current_language), on_click=lambda _: remove_option_field(container))
                ]),
                padding=ft.padding.all(8), bgcolor=ft.Colors.GREY_50, border_radius=ft.border_radius.all(8)
            )
            if text:
                container.content.controls[1].value = text
            if option_id:
                container.content.controls[1].data = option_id
            return container

        def add_option_field():
            options_container.current.controls.append(create_option_field())
            page.update()

        def remove_option_field(option_container):
            if len(options_container.current.controls) > 1:
                options_container.current.controls.remove(option_container)
                page.update()

        def done_question(_):
            if not question_field.current.value or not question_field.current.value.strip():
                question_field.current.error_text = get_text("please_enter_question", current_language)
                page.update()
                return

            options = []
            for option_container in options_container.current.controls:
                option_row = option_container.content
                is_correct = option_row.controls[0].value
                option_text = option_row.controls[1].value
                option_field = option_row.controls[1]
                option_field.error_text = None

                if option_text and option_text.strip():
                    options.append({"text": option_text.strip(), "is_correct": is_correct})
                else:
                    option_field.error_text = get_text("please_enter_option_text", current_language)

            if len(options) < 2:
                show_snackbar(get_text("please_enter_at_least_two_options", current_language), True)
                page.update()
                return

            if not any(option["is_correct"] for option in options):
                show_snackbar(get_text("please_mark_at_least_one_correct", current_language), True)
                page.update()
                return

            question_id = add_question(quiz_id, question_field.current.value, options)
            if question_id:
                show_snackbar(get_text("question_added_successfully", current_language))
                question_field.current.value = ""
                options_container.current.controls.clear()
                add_option_field()
                question_form_visible.current.visible = False
                load_quizzes()
                show_quiz_details(quiz_id)
            else:
                show_snackbar(get_text("failed_to_add_question", current_language), True)
                page.update()

        def toggle_question_form(_):
            if question_form_visible.current.visible:
                question_field.current.value = ""
                question_field.current.error_text = None
                options_container.current.controls.clear()
                add_option_field()

            question_form_visible.current.visible = not question_form_visible.current.visible

            if len(quiz['questions']) == 0:
                empty_state_visible.current.visible = not question_form_visible.current.visible

            page.update()

        def delete_question_simple(question_id):
            if delete_question(question_id):
                show_snackbar(get_text("question_deleted_successfully", current_language))
                load_quizzes()
                show_quiz_details(quiz_id)
            else:
                show_snackbar(get_text("failed_to_delete_question", current_language), True)
                page.update()

        question_form = ft.Container(
            ref=question_form_visible, visible=auto_add_question,
            content=ft.Column([
                ft.Text(get_text("add_new_question", current_language), size=16, weight=ft.FontWeight.BOLD),
                ft.TextField(ref=question_field, hint_text=get_text("enter_your_question_here", current_language), multiline=True),
                ft.Row([
                    ft.Text(get_text("answer_options", current_language), size=14, expand=True),
                    create_button(get_text("add_option", current_language), ft.Icons.ADD, lambda _: add_option_field(), color=ft.Colors.BLUE_600)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                ft.Column(ref=options_container, spacing=8),
                ft.Row([
                    create_button(get_text("cancel", current_language), None, toggle_question_form),
                    create_button(get_text("done", current_language), None, done_question, bgcolor=ft.Colors.GREEN_600)
                ], alignment=ft.MainAxisAlignment.END)
            ]),
            padding=ft.padding.all(20), bgcolor=ft.Colors.WHITE, border_radius=ft.border_radius.all(12)
        )

        options_container.current = question_form.content.controls[3]
        add_option_field()

        questions_list = ft.Column([], spacing=10)

        for i, q in enumerate(quiz['questions']):
            question_card = ft.Card(content=ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Text(f"{i+1}. {q['question_text']}", size=16, weight=ft.FontWeight.BOLD, expand=True),
                        ft.IconButton(icon=ft.Icons.DELETE_OUTLINE, on_click=lambda _, qid=q['id']: delete_question_simple(qid))
                    ]),
                    ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.CHECK_CIRCLE if o['is_correct'] else ft.Icons.RADIO_BUTTON_UNCHECKED, color=ft.Colors.GREEN_600 if o['is_correct'] else ft.Colors.GREY_400),
                            ft.Text(o['option_text'], color=ft.Colors.GREEN_700 if o['is_correct'] else ft.Colors.GREY_700)
                        ])
                        for o in q['options']
                    ])
                ]),
                padding=ft.padding.all(16)
            ))
            questions_list.controls.append(question_card)

        edit_title_field = ft.Ref[ft.TextField]()
        edit_description_field = ft.Ref[ft.TextField]()
        edit_class_dropdown = ft.Ref[ft.Dropdown]()
        edit_subject_dropdown = ft.Ref[ft.Dropdown]()

        def on_edit_class_change(_):
            edit_subject_dropdown.current.value = "select_subject"
            edit_subject_dropdown.current.error_text = None

            if edit_class_dropdown.current.value:
                subjects = get_class_subjects(int(edit_class_dropdown.current.value))
                edit_subject_dropdown.current.options = [ft.dropdown.Option(key="select_subject", text=get_text("select_subject", current_language))] + [ft.dropdown.Option(key=str(s['id']), text=s['subject_name']) for s in subjects]
                edit_subject_dropdown.current.disabled = False
            else:
                edit_subject_dropdown.current.options = [ft.dropdown.Option(key="select_subject", text=get_text("select_subject", current_language))]
                edit_subject_dropdown.current.disabled = True

            page.update()

        def save_quiz_details(_):
            fields = [
                (edit_title_field.current, get_text("please_enter_quiz_title", current_language)),
                (edit_class_dropdown.current, get_text("please_select_class", current_language)),
                (edit_subject_dropdown.current, get_text("please_select_subject", current_language))
            ]

            if not validate_fields(fields):
                page.update()
                return

            success = update_quiz_details(quiz_id, edit_title_field.current.value.strip(), edit_description_field.current.value.strip(), edit_class_dropdown.current.value, edit_subject_dropdown.current.value)
            if success:
                show_snackbar(get_text("quiz_details_updated_successfully", current_language))
                load_quizzes()
                close_dialog(page)
            else:
                show_snackbar(get_text("failed_to_update_quiz_details", current_language), True)
            page.update()

        # Create responsive header layout
        edit_dropdowns_layout = ft.Column([
            ft.Dropdown(ref=edit_class_dropdown, hint_text=get_text("select_class", current_language), options=[ft.dropdown.Option(key=str(info['id']), text=name) for name, info in get_existing_classes().items()], value=str(quiz['class_id']) if quiz['class_id'] else None, expand=True, on_change=on_edit_class_change),
            ft.Dropdown(ref=edit_subject_dropdown, hint_text=get_text("select_subject", current_language), options=[ft.dropdown.Option(key="select_subject", text=get_text("select_subject", current_language))] + ([ft.dropdown.Option(key=str(s['id']), text=s['subject_name']) for s in get_class_subjects(quiz['class_id'])] if quiz['class_id'] else []), value=str(quiz['subject_id']) if quiz['subject_id'] else "select_subject", expand=True)
        ], spacing=10) if is_mobile else ft.Row([
            ft.Dropdown(ref=edit_class_dropdown, hint_text=get_text("select_class", current_language), options=[ft.dropdown.Option(key=str(info['id']), text=name) for name, info in get_existing_classes().items()], value=str(quiz['class_id']) if quiz['class_id'] else None, expand=True, on_change=on_edit_class_change),
            ft.Dropdown(ref=edit_subject_dropdown, hint_text=get_text("select_subject", current_language), options=[ft.dropdown.Option(key="select_subject", text=get_text("select_subject", current_language))] + ([ft.dropdown.Option(key=str(s['id']), text=s['subject_name']) for s in get_class_subjects(quiz['class_id'])] if quiz['class_id'] else []), value=str(quiz['subject_id']) if quiz['subject_id'] else "select_subject", expand=True)
        ])

        quiz_header = ft.Column([
            ft.TextField(ref=edit_title_field, value=quiz['title'], hint_text=get_text("quiz_title", current_language)),
            ft.TextField(ref=edit_description_field, value=quiz['description'] or "", hint_text=get_text("description_optional", current_language), multiline=True),
            edit_dropdowns_layout
        ])

        # Responsive dialog dimensions
        dialog_width = page.width * 0.95 if is_mobile else 700
        dialog_height = page.height * 0.8 if is_mobile else 500

        dialog = ft.AlertDialog(
            title=quiz_header,
            content=ft.Column([
                ft.Text(get_text("questions_label", current_language).format(count=len(quiz['questions'])), size=16, weight=ft.FontWeight.BOLD),
                questions_list if quiz['questions'] else ft.Container(
                    ref=empty_state_visible, visible=not auto_add_question,
                    content=ft.Column([
                        ft.Icon(ft.Icons.QUIZ, size=48, color=ft.Colors.BLUE_600),
                        ft.Text(get_text("ready_to_create_questions", current_language), size=18, weight=ft.FontWeight.BOLD),
                        ft.Text(get_text("add_engaging_questions", current_language), size=14, color=ft.Colors.GREY_600),
                        create_button(get_text("create_your_first_question", current_language), ft.Icons.ADD_CIRCLE, toggle_question_form, bgcolor=ft.Colors.GREEN_600)
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=16),
                    padding=ft.padding.all(20), alignment=ft.alignment.center
                ),
                question_form,
                ft.Container(content=create_button(get_text("add_question", current_language), ft.Icons.ADD, toggle_question_form), alignment=ft.alignment.center, visible=len(quiz['questions']) > 0)
            ], scroll=ft.ScrollMode.AUTO, height=dialog_height, width=dialog_width),
            actions=[ft.Row([create_button(get_text("save", current_language), None, save_quiz_details, bgcolor=ft.Colors.BLUE_600)], alignment=ft.MainAxisAlignment.END)]
        )
        show_dialog(page, dialog)

    def show_quiz_qr_code(quiz_id, quiz_title):
        """
        Show quiz QR code on the face display and provide a dialog to close quiz.
        Args:
            quiz_id: The quiz ID
            quiz_title: The quiz title
        """
        # Start the quiz server if it's not already running
        _, quiz_port = start_quiz_server()
        # Get local IP address
        from gui.utils.network import get_local_ip
        local_ip = get_local_ip()
        # Generate quiz URL with the correct port
        full_url = generate_quiz_url(quiz_id, quiz_title, host=local_ip, port=quiz_port, shorten=False)
        short_url = generate_quiz_url(quiz_id, quiz_title, host=local_ip, port=quiz_port, shorten=True, verify_ssl=False)

        # Switch the face display to show quiz QR code
        from gui.services.qr_display_service import switch_to_quiz_display
        success = switch_to_quiz_display(full_url, quiz_title)

        if not success:
            print(f"❌ Failed to display quiz QR code for quiz: {quiz_title}")
            # Show error message
            snack_bar = ft.SnackBar(
                content=ft.Text(get_text("failed_to_display_qr_code", current_language)),
                action="OK",
                action_color=ft.Colors.ERROR,
                duration=3000
            )
            page.overlay.append(snack_bar)
            snack_bar.open = True
            page.update()
            return

        print(f"✅ {get_text('quiz_qr_code_displayed', current_language).format(title=quiz_title)}")

        # Create quiz control dialog content
        content = ft.Container(
            content=ft.Column([
                # Status indicator
                ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.Icons.CIRCLE, color=ft.Colors.GREEN, size=16),
                        ft.Text(get_text("quiz_active", current_language), size=16, color=ft.Colors.BLACK, weight=ft.FontWeight.BOLD),
                    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                    padding=ft.padding.all(12),
                    border_radius=20,
                    bgcolor=ft.Colors.GREEN_50,
                    margin=ft.margin.only(bottom=20),
                ),
                # Information
                ft.Text(
                    get_text("qr_code_showing_on_robot", current_language).format(title=quiz_title),
                    size=14,
                    text_align=ft.TextAlign.CENTER,
                    weight=ft.FontWeight.W_500
                ),
                ft.Text(
                    get_text("students_can_scan_to_take_quiz", current_language),
                    size=12,
                    text_align=ft.TextAlign.CENTER,
                    color=ft.Colors.GREY_600
                ),
                # Share link section
                ft.Container(
                    content=ft.Column([
                        ft.Text(get_text("or_share_this_link", current_language), size=12, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER),
                        ft.Container(
                            content=ft.Text(
                                short_url,
                                size=11,
                                color=ft.Colors.PRIMARY,
                                text_align=ft.TextAlign.CENTER,
                                selectable=True
                            ),
                            padding=ft.padding.all(8),
                            bgcolor=ft.Colors.SURFACE,
                            border_radius=4,
                        ),
                    ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    padding=ft.padding.all(12),
                    bgcolor=ft.Colors.BLUE_GREY_50,
                    border_radius=8,
                    margin=ft.margin.only(top=20),
                ),
            ], spacing=10, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            padding=20,
            width=400 if not is_mobile else page.width * 0.9,
        )

        # Function to handle dialog close and stop quiz server
        def handle_close(_):
            # Stop the quiz server when dialog is closed
            from gui.services.qr_display_service import switch_back_to_face_from_quiz

            stop_quiz_server()
            switch_back_to_face_from_quiz()

            # Close the dialog
            close_dialog(page)

            # Show confirmation message
            snack_bar = ft.SnackBar(
                content=ft.Text(get_text("quiz_closed_message", current_language).format(title=quiz_title)),
                action="OK",
                action_color=ft.Colors.PRIMARY,
                duration=4000
            )
            page.overlay.append(snack_bar)
            snack_bar.open = True
            page.update()

        # Create quiz control dialog
        dialog = ft.AlertDialog(
            title=ft.Text(
                get_text("quiz_active_title", current_language).format(title=quiz_title),
                weight=ft.FontWeight.BOLD,
                size=18,
            ),
            content=content,
            actions=[
                ft.ElevatedButton(
                    get_text("close_quiz", current_language),
                    icon=ft.Icons.STOP,
                    on_click=handle_close,
                    style=ft.ButtonStyle(
                        color=ft.Colors.WHITE,
                        bgcolor=ft.Colors.RED_500,
                        shape=ft.RoundedRectangleBorder(radius=8),
                    ),
                    height=40,
                ),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
            modal=True,
            actions_padding=ft.padding.all(20),
            content_padding=ft.padding.all(20),
        )

        # Add on_dismiss handler to stop quiz server if dialog is closed
        def on_dismiss(_):
            print(get_text("quiz_dialog_dismissed", current_language))
            # Stop the quiz server when dialog is dismissed
            from gui.services.qr_display_service import switch_back_to_face_from_quiz

            stop_quiz_server()
            switch_back_to_face_from_quiz()

            # Show confirmation message
            snack_bar = ft.SnackBar(
                content=ft.Text(get_text("quiz_closed_message", current_language).format(title=quiz_title)),
                action="OK",
                action_color=ft.Colors.PRIMARY,
                duration=4000
            )
            page.overlay.append(snack_bar)
            snack_bar.open = True
            page.update()

        dialog.on_dismiss = on_dismiss

        # Show the dialog
        show_dialog(page, dialog)

    def show_quiz_results(quiz_id, quiz_title):
        submissions = get_quiz_submissions(quiz_id)

        def delete_submission(submission_id, student_name):
            if delete_quiz_submission(submission_id):
                show_snackbar(get_text("result_deleted", current_language).format(name=student_name))
                show_quiz_results(quiz_id, quiz_title)
            else:
                show_snackbar(get_text("failed_to_delete_result", current_language), True)

        # Responsive dialog dimensions
        dialog_width = page.width * 0.95 if is_mobile else 600
        results_height = page.height * 0.5 if is_mobile else 400

        if not submissions:
            # Show empty state dialog
            empty_state = ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.INBOX, size=48, color=ft.Colors.GREY_600),
                    ft.Text(get_text("no_quiz_results_yet", current_language), size=18, weight=ft.FontWeight.BOLD),
                    ft.Text(get_text("students_havent_submitted", current_language), size=14, color=ft.Colors.GREY_600),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=16),
                padding=ft.padding.all(30 if is_mobile else 40), alignment=ft.alignment.center
            )

            dialog = ft.AlertDialog(
                title=ft.Text(get_text("quiz_results_title", current_language).format(title=quiz_title), size=18 if is_mobile else 20, weight=ft.FontWeight.BOLD),
                content=ft.Column([
                    ft.Text(get_text("total_submissions", current_language).format(count=0), size=16, weight=ft.FontWeight.BOLD),
                    ft.Divider(),
                    empty_state
                ], width=dialog_width),
                actions=[
                    ft.TextButton(get_text("close", current_language), on_click=lambda _: close_dialog(page))
                ]
            )
            show_dialog(page, dialog)
            return

        results_list = ft.Column([], spacing=10, scroll=ft.ScrollMode.AUTO, height=results_height)

        for submission in submissions:
            percentage = round((submission['score'] / submission['total_questions']) * 100) if submission['total_questions'] > 0 else 0

            # Format date
            from datetime import datetime
            try:
                date_obj = datetime.fromisoformat(submission['submitted_at'])
                formatted_date = date_obj.strftime("%Y-%m-%d %H:%M")
            except:
                formatted_date = submission['submitted_at']

            if percentage >= 80:
                score_color = ft.Colors.GREEN_600
                score_icon = ft.Icons.CHECK_CIRCLE
            elif percentage >= 60:
                score_color = ft.Colors.ORANGE_600
                score_icon = ft.Icons.WARNING
            else:
                score_color = ft.Colors.RED_600
                score_icon = ft.Icons.ERROR

            # Create responsive action buttons for mobile
            if is_mobile:
                action_buttons = ft.Column([
                    ft.ElevatedButton(
                        get_text("view_answers", current_language),
                        icon=ft.Icons.VISIBILITY,
                        on_click=lambda _, s_id=submission['id'], s_name=submission['student_name']: show_student_answers(s_id, s_name, quiz_title),
                        expand=True
                    ),
                    ft.ElevatedButton(
                        get_text("delete_result", current_language),
                        icon=ft.Icons.DELETE_OUTLINE,
                        on_click=lambda _, s_id=submission['id'], s_name=submission['student_name']: delete_submission(s_id, s_name),
                        style=ft.ButtonStyle(bgcolor=ft.Colors.RED_600, color=ft.Colors.WHITE),
                        expand=True
                    )
                ], spacing=8)
            else:
                action_buttons = ft.Row([
                    ft.IconButton(
                        icon=ft.Icons.VISIBILITY,
                        tooltip=get_text("view_detailed_answers", current_language),
                        on_click=lambda _, s_id=submission['id'], s_name=submission['student_name']: show_student_answers(s_id, s_name, quiz_title)
                    ),
                    ft.IconButton(
                        icon=ft.Icons.DELETE_OUTLINE,
                        tooltip=get_text("delete_result_tooltip", current_language),
                        icon_color=ft.Colors.RED_600,
                        on_click=lambda _, s_id=submission['id'], s_name=submission['student_name']: delete_submission(s_id, s_name)
                    )
                ], tight=True)

            student_card = ft.Card(content=ft.Container(
                content=ft.Column([
                    ft.ListTile(
                        leading=ft.Icon(score_icon, color=score_color, size=30),
                        title=ft.Text(submission['student_name'], weight=ft.FontWeight.BOLD),
                        subtitle=ft.Column([
                            ft.Text(get_text("score_display", current_language).format(score=submission['score'], total=submission['total_questions'], percentage=percentage), color=score_color),
                            ft.Text(get_text("date_display", current_language).format(date=formatted_date), size=12, color=ft.Colors.GREY_600)
                        ], spacing=2)
                    ),
                    action_buttons if is_mobile else ft.Container()
                ] if is_mobile else [
                    ft.ListTile(
                        leading=ft.Icon(score_icon, color=score_color, size=30),
                        title=ft.Text(submission['student_name'], weight=ft.FontWeight.BOLD),
                        subtitle=ft.Column([
                            ft.Text(get_text("score_display", current_language).format(score=submission['score'], total=submission['total_questions'], percentage=percentage), color=score_color),
                            ft.Text(get_text("date_display", current_language).format(date=formatted_date), size=12, color=ft.Colors.GREY_600)
                        ], spacing=2),
                        trailing=action_buttons
                    )
                ]),
                padding=ft.padding.all(8)
            ))
            results_list.controls.append(student_card)

        dialog = ft.AlertDialog(
            title=ft.Text(get_text("quiz_results_title", current_language).format(title=quiz_title), size=18 if is_mobile else 20, weight=ft.FontWeight.BOLD),
            content=ft.Column([
                ft.Text(get_text("total_submissions", current_language).format(count=len(submissions)), size=16, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                results_list
            ], width=dialog_width),
            actions=[
                ft.TextButton(get_text("close", current_language), on_click=lambda _: close_dialog(page))
            ]
        )
        show_dialog(page, dialog)

    def show_student_answers(submission_id, student_name, quiz_title):
        submission_details = get_submission_details(submission_id)

        if not submission_details:
            show_snackbar(get_text("could_not_load_student_answers", current_language), True)
            page.update()
            return

        # Responsive dimensions
        answers_height = page.height * 0.5 if is_mobile else 400
        answers_list = ft.Column([], spacing=15, scroll=ft.ScrollMode.AUTO, height=answers_height)

        for answer in submission_details['answers']:
            is_correct = answer['is_correct']

            answer_card = ft.Card(content=ft.Container(
                content=ft.Column([
                    ft.Text(f"Q{answer['question_order']}: {answer['question_text']}",
                           size=14, weight=ft.FontWeight.BOLD),
                    ft.Row([
                        ft.Icon(ft.Icons.CHECK_CIRCLE if is_correct else ft.Icons.CANCEL,
                               color=ft.Colors.GREEN_600 if is_correct else ft.Colors.RED_600),
                        ft.Text(get_text("student_answer_label", current_language), weight=ft.FontWeight.BOLD, size=12),
                        ft.Text(answer['selected_option_text'] or get_text("no_answer", current_language),
                               color=ft.Colors.GREEN_700 if is_correct else ft.Colors.RED_700)
                    ]),
                    ft.Row([
                        ft.Icon(ft.Icons.LIGHTBULB, color=ft.Colors.BLUE_600),
                        ft.Text(get_text("correct_answer_label", current_language), weight=ft.FontWeight.BOLD, size=12),
                        ft.Text(answer['correct_option_text'], color=ft.Colors.BLUE_700)
                    ]) if not is_correct else ft.Container()
                ]),
                padding=ft.padding.all(12),
                bgcolor=ft.Colors.GREEN_50 if is_correct else ft.Colors.RED_50,
                border_radius=ft.border_radius.all(8)
            ))
            answers_list.controls.append(answer_card)

        percentage = round((submission_details['score'] / submission_details['total_questions']) * 100) if submission_details['total_questions'] > 0 else 0

        # Responsive dialog width
        dialog_width = page.width * 0.95 if is_mobile else 600

        dialog = ft.AlertDialog(
            title=ft.Text(get_text("student_answers_title", current_language).format(name=student_name, title=quiz_title), size=16 if is_mobile else 18, weight=ft.FontWeight.BOLD),
            content=ft.Column([
                ft.Container(
                    content=ft.Row([
                        ft.Text(get_text("final_score", current_language).format(score=submission_details['score'], total=submission_details['total_questions'], percentage=percentage),
                               size=16, weight=ft.FontWeight.BOLD,
                               color=ft.Colors.GREEN_600 if percentage >= 60 else ft.Colors.RED_600)
                    ], alignment=ft.MainAxisAlignment.CENTER),
                    padding=ft.padding.all(10),
                    bgcolor=ft.Colors.GREY_100,
                    border_radius=ft.border_radius.all(8)
                ),
                ft.Divider(),
                answers_list
            ], width=dialog_width),
            actions=[
                ft.TextButton(get_text("back_to_results", current_language), on_click=lambda _: (close_dialog(page), show_quiz_results(submission_details['quiz_id'], quiz_title))),
                ft.TextButton(get_text("close", current_language), on_click=lambda _: close_dialog(page))
            ]
        )
        show_dialog(page, dialog)

    def confirm_delete_quiz(quiz_id, quiz_title):
        def delete_quiz_func(_=None):
            if delete_quiz(quiz_id):
                show_snackbar(f"{quiz_title} {get_text('deleted', current_language)}")
                load_quizzes()
            else:
                show_snackbar(f"Failed to delete {quiz_title}", True)
            close_dialog(page)
            page.update()

        dialog = create_confirmation_dialog(page, get_text("delete_quiz", current_language), get_text("are_you_sure_delete_quiz", current_language).format(title=quiz_title), delete_quiz_func)
        show_dialog(page, dialog)

    load_quizzes()

    # Modern content layout with welcome section
    content = [
        welcome_section,
        ft.Container(
            content=filter_section,
            alignment=ft.alignment.center,
        ),
        ft.Container(
            content=quiz_form,
            alignment=ft.alignment.center,
        ),
        ft.Container(
            content=quizzes_list,
            alignment=ft.alignment.center,
        )
    ]

    return create_page_layout(page, "", content)  # Empty title since we have the welcome section
