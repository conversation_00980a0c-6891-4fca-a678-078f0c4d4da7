"""
Settings view for the application.
"""
import flet as ft
from gui.components.layout import create_page_layout
from gui.config.language import get_text, LANGUAGE_NAMES, DEFAULT_LANGUAGE

def create_settings_view(page: ft.Page):
    """
    Create the settings view.

    Args:
        page: The Flet page object

    Returns:
        ft.View: The settings view
    """
    # Get current language
    current_language = getattr(page, 'language', DEFAULT_LANGUAGE)

    # Create language dropdown
    language_dropdown = ft.Dropdown(
        label=get_text("select_language", current_language),
        value=current_language,
        options=[
            ft.dropdown.Option(key=lang_code, text=name)
            for lang_code, name in LANGUAGE_NAMES.items()
        ],
        width=250
    )

    # Theme switch removed as requested

    # Save settings function
    def save_settings(_):
        # Get selected language
        selected_language = language_dropdown.value

        # Update language if changed
        if selected_language != current_language:
            if hasattr(page, 'app_state'):
                page.app_state.set_language(selected_language)
            else:
                # Fallback
                page.language = selected_language

    # Create settings content
    settings_content = ft.Column([
        # Language section
        ft.Container(
            content=ft.Column([
                ft.Text(
                    get_text("language", current_language),
                    size=18,
                    weight=ft.FontWeight.BOLD
                ),
                language_dropdown,
            ]),
            padding=ft.padding.all(20),
            margin=ft.margin.only(bottom=10),
            border_radius=10,
            border=ft.border.all(1, ft.Colors.OUTLINE_VARIANT),
        ),

        # Save button
        ft.ElevatedButton(
            text=get_text("save", current_language),
            on_click=save_settings,
            width=200,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
            ),
        ),
    ],
    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
    spacing=10)

    # Create the settings view
    return create_page_layout(
        page,
        get_text("settings", current_language),
        settings_content
    )
