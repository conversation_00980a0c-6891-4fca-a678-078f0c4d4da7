"""
Login view for user authentication.
"""
import flet as ft
from gui.services.auth_service import AuthService
from gui.config.constants import ROUTE_DASHBOARD, ROUTE_ADMIN_DASHBOARD

def create_login_view(page: ft.Page):
    """Create the login page with modern, simple design."""
    auth_service = AuthService()

    # State variables with French labels
    username_field = ft.TextField(
        label="Nom d'utilisateur",
        width=320,
        prefix_icon=ft.Icons.PERSON,
        border_radius=12,
        filled=True,
        bgcolor=ft.Colors.WHITE,
        border_color=ft.Colors.BLUE_200,
        focused_border_color=ft.Colors.BLUE_600,
        content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
        text_style=ft.TextStyle(size=16),
        label_style=ft.TextStyle(size=14, color=ft.Colors.BLUE_700)
    )

    password_field = ft.TextField(
        label="Mot de passe",
        width=320,
        prefix_icon=ft.Icons.LOCK,
        password=True,
        can_reveal_password=True,
        border_radius=12,
        filled=True,
        bgcolor=ft.Colors.WHITE,
        border_color=ft.Colors.BLUE_200,
        focused_border_color=ft.Colors.BLUE_600,
        content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
        text_style=ft.TextStyle(size=16),
        label_style=ft.TextStyle(size=14, color=ft.Colors.BLUE_700)
    )

    error_text = ft.Text(
        "",
        color=ft.Colors.RED_400,
        size=14,
        visible=False
    )

    login_button = ft.ElevatedButton(
        text="Se connecter",
        width=320,
        height=54,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            text_style=ft.TextStyle(size=17, weight=ft.FontWeight.W_600),
            shape=ft.RoundedRectangleBorder(radius=12),
            elevation=4,
            shadow_color=ft.Colors.BLUE_200
        ),
        disabled=False
    )

    def show_error(message: str):
        """Show error message."""
        error_text.value = message
        error_text.visible = True
        page.update()

    def hide_error():
        """Hide error message."""
        error_text.visible = False
        page.update()

    def on_login_click(e):
        """Handle login button click."""
        hide_error()

        username = username_field.value.strip()
        password = password_field.value.strip()

        if not username or not password:
            show_error("Veuillez entrer le nom d'utilisateur et le mot de passe")
            return

        # Show loading state
        login_button.text = "Connexion en cours..."
        login_button.disabled = True
        page.update()

        # Authenticate user
        user = auth_service.authenticate(username, password)

        if user:
            # Store user info in page state
            page.session.set("current_user", user)
            page.app_state.current_user = user

            # Redirect based on role
            if user['role'] == 'admin':
                page.go(ROUTE_ADMIN_DASHBOARD)
            else:
                page.go(ROUTE_DASHBOARD)
        else:
            show_error("Nom d'utilisateur ou mot de passe incorrect")

        # Reset button state
        login_button.text = "Se connecter"
        login_button.disabled = False
        page.update()

    def on_enter_key(e):
        """Handle Enter key press in form fields."""
        if e.key == "Enter":
            on_login_click(e)

    # Admin access button with French text
    admin_button = ft.TextButton(
        text="Accès Administrateur",
        style=ft.ButtonStyle(
            color=ft.Colors.BLUE_600,
            text_style=ft.TextStyle(size=15, weight=ft.FontWeight.W_600),
            bgcolor=ft.Colors.BLUE_50,
            shape=ft.RoundedRectangleBorder(radius=8),
            padding=ft.padding.symmetric(horizontal=16, vertical=8)
        ),
        on_click=lambda _: admin_login()
    )

    def admin_login():
        """Quick admin login."""
        username_field.value = "admin"
        password_field.value = "admin"
        page.update()
        on_login_click(None)

    # Add event handlers
    login_button.on_click = on_login_click
    username_field.on_submit = on_login_click
    password_field.on_submit = on_login_click

    # App logo/icon
    logo_container = ft.Container(
        content=ft.Icon(
            ft.Icons.SCHOOL,
            size=80,
            color=ft.Colors.BLUE_600
        ),
        margin=ft.margin.only(bottom=30)
    )

    # Welcome text with French
    welcome_text = ft.Text(
        "Assistant Enseignant",
        size=34,
        weight=ft.FontWeight.BOLD,
        color=ft.Colors.BLUE_900,
        text_align=ft.TextAlign.CENTER
    )

    subtitle_text = ft.Text(
        "Veuillez vous connecter pour continuer",
        size=17,
        color=ft.Colors.BLUE_GREY_600,
        text_align=ft.TextAlign.CENTER,
        weight=ft.FontWeight.W_400
    )

    # Login form container with improved styling
    login_form = ft.Container(
        content=ft.Column([
            logo_container,
            welcome_text,
            ft.Container(height=12),
            subtitle_text,
            ft.Container(height=40),
            username_field,
            ft.Container(height=24),
            password_field,
            ft.Container(height=12),
            error_text,
            ft.Container(height=24),
            login_button,
            ft.Container(height=20),
            admin_button,
        ],
        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        spacing=0),
        padding=ft.padding.all(44),
        bgcolor=ft.Colors.WHITE,
        border_radius=24,
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=20,
            color=ft.Colors.with_opacity(0.15, ft.Colors.BLUE_600),
            offset=ft.Offset(0, 8)
        ),
        width=420,
        border=ft.border.all(1, ft.Colors.BLUE_100)
    )

    # Main content container
    main_content = ft.Container(
        content=ft.Column([
            ft.Container(height=50),  # Top spacing
            login_form,
        ],
        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        spacing=0),
        alignment=ft.alignment.center,
        expand=True
    )

    # Gradient background container
    background_container = ft.Container(
        content=main_content,
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.BLUE_50, ft.Colors.WHITE, ft.Colors.BLUE_50]
        ),
        expand=True
    )

    # Create the view
    view = ft.View(
        route="/login",
        controls=[background_container],
        padding=0,
        spacing=0
    )

    return view
