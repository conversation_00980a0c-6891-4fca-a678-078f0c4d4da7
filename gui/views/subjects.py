"""
Subjects view for the application.
"""
import flet as ft
from datetime import datetime
from gui.components.layout import create_page_layout

from gui.components.dialogs import create_confirmation_dialog, create_form_dialog, show_dialog
from gui.services.subject_service import get_class_subjects, create_class_subject, update_class_subject, delete_class_subject
from gui.services.class_service import get_existing_classes
from gui.services.attendance_service import get_attendance_records
from gui.state import AppState
from gui.config.constants import ICON_ADD, ICON_SEARCH, ICON_CLEAR
from gui.config.language import get_text

def create_subjects_view(page: ft.Page):
    """
    Create the subjects management view.

    Args:
        page: The Flet page object

    Returns:
        ft.View: The subjects view
    """
    # Initialize app state if not already done
    if getattr(page, 'app_state', None) is None:
        page.app_state = AppState(page)

    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # Get current user and filter data by teacher
    current_user = getattr(page.app_state, 'current_user', None)
    teacher_id = current_user['id'] if current_user and current_user['role'] == 'teacher' else None

    # Admin can see all data, teachers only see their own
    if current_user and current_user['role'] == 'admin':
        subjects = get_class_subjects()
        classes = get_existing_classes()
    else:
        subjects = get_class_subjects(teacher_id=teacher_id)
        classes = get_existing_classes(teacher_id)

    # Modern welcome section with gradient background
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Text(
                get_text("subjects", current_language),
                size=28,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.WHITE,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                f"{len(subjects)} subjects available",
                size=16,
                color=ft.Colors.WHITE70,
                text_align=ft.TextAlign.CENTER
            )
        ],
        spacing=8,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20, top=0),
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.ORANGE_600, ft.Colors.RED_600]
        ),
        border_radius=ft.border_radius.all(20),
        alignment=ft.alignment.center
    )

    subject_cards_container = ft.Column(
        spacing=16,
        width=page.width*0.9 if is_mobile else 600,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER
    )

    # Function to filter subjects based on search and class filter
    def filter_subjects(all_subjects, search_query="", class_filter_value=None):
        """Filter subjects based on search query and class filter"""
        filtered = all_subjects

        # Apply search filter
        if search_query:
            search_query = search_query.lower().strip()
            filtered = [
                subject for subject in filtered
                if search_query in subject['subject_name'].lower()
            ]

        # Apply class filter
        if class_filter_value and class_filter_value != "all":
            filtered = [
                subject for subject in filtered
                if str(subject['class_id']) == str(class_filter_value)
            ]

        return filtered

    # Function to refresh subject cards dynamically
    def refresh_subject_cards(search_query="", class_filter_value=None):
        """Refresh the subject cards container with filtered data"""
        # Clear existing cards
        subject_cards_container.controls.clear()

        # Get fresh data from database with teacher filtering
        current_user = getattr(page.app_state, 'current_user', None)
        teacher_id = current_user['id'] if current_user and current_user['role'] == 'teacher' else None

        if current_user and current_user['role'] == 'admin':
            fresh_subjects = get_class_subjects()
            fresh_classes = get_existing_classes()
        else:
            fresh_subjects = get_class_subjects(teacher_id=teacher_id)
            fresh_classes = get_existing_classes(teacher_id)

        # Filter subjects
        filtered_subjects = filter_subjects(fresh_subjects, search_query, class_filter_value)

        # Update welcome section with new count
        if search_query or (class_filter_value and class_filter_value != "all"):
            welcome_section.content.controls[1].value = f"{len(filtered_subjects)} {get_text('of_subjects_found', current_language).format(total=len(fresh_subjects))}"
        else:
            welcome_section.content.controls[1].value = f"{len(fresh_subjects)} {get_text('subjects_available', current_language)}"

        # Create subject cards grouped by class
        if filtered_subjects:
            # Group subjects by class
            subjects_by_class = {}
            for subject in filtered_subjects:
                class_id = subject['class_id']
                class_name = next(
                    (name for name, info in fresh_classes.items() if info['id'] == class_id),
                    get_text("unknown_class", current_language)
                )

                if class_name not in subjects_by_class:
                    subjects_by_class[class_name] = []
                subjects_by_class[class_name].append(subject)

            # Add header for subject list with filter context
            filter_text = ""
            if class_filter_value and class_filter_value != "all":
                filter_text = f" in {next((name for name, info in fresh_classes.items() if str(info['id']) == str(class_filter_value)), get_text('unknown_class', current_language))}"
            if search_query:
                filter_text += f" ({get_text('searching', current_language)}: '{search_query}')"

            subject_cards_container.controls.append(
                ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.Icons.LIST_ALT, color=ft.Colors.BLUE_600),
                        ft.Text(get_text("your_subjects", current_language).format(count=len(filtered_subjects)) + filter_text, weight=ft.FontWeight.BOLD, size=16),
                    ]),
                    margin=ft.margin.only(bottom=16),
                )
            )

            # Create grouped display
            for class_name, class_subjects in subjects_by_class.items():
                # Add class header (only if not filtering by specific class)
                if not class_filter_value or class_filter_value == "all":
                    subject_cards_container.controls.append(
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.SCHOOL, color=ft.Colors.GREEN_600, size=20),
                                ft.Text(f"{class_name} ({len(class_subjects)} {get_text('subjects', current_language).lower()})",
                                       weight=ft.FontWeight.BOLD, size=14, color=ft.Colors.GREEN_700),
                            ]),
                            padding=ft.padding.symmetric(horizontal=8, vertical=4),
                            margin=ft.margin.only(top=8, bottom=8),
                            bgcolor=ft.Colors.GREEN_50,
                            border_radius=8,
                        )
                    )

                # Add subjects for this class
                for subject in class_subjects:
                    subject_id = subject['id']
                    subject_name = subject['subject_name']
                    teacher_name = subject.get('teacher_name', "")

                    # Create enhanced subject card
                    menu_items = [
                        ft.PopupMenuItem(text=get_text("attendance_history", current_language), icon=ft.Icons.HISTORY, on_click=create_attendance_history_handler(subject_id, subject_name, class_name)),
                        ft.PopupMenuItem(),  # Divider
                        ft.PopupMenuItem(text=get_text("edit_subject", current_language), icon=ft.Icons.EDIT, on_click=create_edit_handler(subject_id, subject_name, teacher_name)),
                        ft.PopupMenuItem(text=get_text("delete_subject", current_language), icon=ft.Icons.DELETE_OUTLINE, on_click=create_delete_handler(subject_id, subject_name)),
                    ]

                    # Show class name only if not filtering by class
                    subtitle_text = f"{get_text('teacher', current_language)}: {teacher_name}" if teacher_name else get_text("no_teacher_assigned", current_language)
                    if not class_filter_value or class_filter_value == "all":
                        subtitle_text = f"{class_name} • {subtitle_text}"

                    subject_card = ft.Card(
                        content=ft.Container(
                            content=ft.Column([
                                ft.Row([
                                    ft.Icon(ft.Icons.BOOK, color=ft.Colors.ORANGE_600, size=24),
                                    ft.Column([
                                        ft.Text(subject_name, weight=ft.FontWeight.BOLD, size=16),
                                        ft.Text(subtitle_text, size=12, color=ft.Colors.GREY_600),
                                    ], expand=True, spacing=2),
                                    ft.PopupMenuButton(
                                        icon=ft.Icons.MORE_VERT,
                                        items=menu_items,
                                    ),
                                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                            ]),
                            padding=ft.padding.all(16),
                            width=500,
                        ),
                        elevation=2,
                        margin=ft.margin.only(left=16 if not class_filter_value or class_filter_value == "all" else 6, right=6, top=3, bottom=3),
                    )

                    subject_cards_container.controls.append(subject_card)
        else:
            # Add empty state
            empty_message = get_text("no_subjects_found", current_language)
            if class_filter_value and class_filter_value != "all":
                empty_message = get_text("no_subjects_match_filters", current_language)
            elif search_query:
                empty_message = get_text("no_subjects_found_for_query", current_language).format(query=search_query)
            elif not fresh_subjects:
                empty_message = get_text("no_subjects_yet", current_language)

            subject_cards_container.controls.append(
                ft.Container(
                    content=ft.Column([
                        ft.Icon(ft.Icons.BOOK, size=48, color=ft.Colors.GREY_400),
                        ft.Text(empty_message, weight=ft.FontWeight.BOLD, size=18, color=ft.Colors.GREY_600),
                        ft.Text(get_text("try_adjusting_filters", current_language), size=14, color=ft.Colors.GREY_500),
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=12),
                    padding=ft.padding.all(40),
                    alignment=ft.alignment.center,
                    border=ft.border.all(2, ft.Colors.GREY_300),
                    border_radius=12,
                    bgcolor=ft.Colors.GREY_50,
                )
            )

        page.update()

    # Function to handle search changes
    def on_search_change(_):
        """Handle search field changes"""
        search_query = search_field.value if search_field.value else ""
        class_filter_value = class_filter_dropdown.value if class_filter_dropdown.value else "all"
        refresh_subject_cards(search_query, class_filter_value)

    # Function to handle class filter changes
    def on_class_filter_change(_):
        """Handle class filter changes"""
        search_query = search_field.value if search_field.value else ""
        class_filter_value = class_filter_dropdown.value if class_filter_dropdown.value else "all"
        refresh_subject_cards(search_query, class_filter_value)

    # Create search field
    search_field = ft.TextField(
        hint_text=get_text("search_subjects", current_language),
        prefix_icon=ft.Icons.SEARCH,
        expand=True,
        on_change=on_search_change,
    )

    # Create class filter dropdown
    class_filter_options = [ft.dropdown.Option(key="all", text=get_text("all_classes", current_language))]
    class_filter_options.extend([
        ft.dropdown.Option(key=str(class_info['id']), text=class_name)
        for class_name, class_info in classes.items()
    ])

    class_filter_dropdown = ft.Dropdown(
        hint_text=get_text("filter_by_class", current_language),
        options=class_filter_options,
        value="all",
        expand=True,
        on_change=on_class_filter_change,
    )

    # Create filter section with same design as quizzes page
    filter_section = ft.Container(
        content=ft.Column([
            ft.Text(
                get_text("filter_subjects", current_language),
                size=16,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.ON_SURFACE
            ),
            ft.Row([search_field, class_filter_dropdown], spacing=12) if not is_mobile else
            ft.Column([search_field, class_filter_dropdown], spacing=12),
        ], spacing=12),
        width=page.width*0.9 if is_mobile else 600,
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20),
        bgcolor=ft.Colors.SURFACE,
        border_radius=ft.border_radius.all(12),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=4,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 1)
        ),
    )

    # Create button handlers (defined outside the loop to avoid closure issues)
    def create_attendance_history_handler(subject_id, subject_name, class_name):
        def handle_attendance_history_click(_):
            show_subject_attendance_history(page, subject_id, subject_name, class_name)
        return handle_attendance_history_click

    def create_edit_handler(subject_id, subject_name, teacher_name):
        def handle_edit_click(_):
            show_edit_subject_dialog(page, subject_id, subject_name, teacher_name)
        return handle_edit_click

    def create_delete_handler(subject_id, subject_name):
        def handle_delete_click(_):
            show_delete_subject_dialog(page, subject_id, subject_name)
        return handle_delete_click

    # Form fields
    class_dropdown = ft.Dropdown(
        label=get_text("select_class", current_language),
        options=[
            ft.dropdown.Option(key=class_info['id'], text=class_name)
            for class_name, class_info in classes.items()
        ],
        expand=True,
        width=page.width*0.9 if is_mobile else 600,
    )

    subject_name_field = ft.TextField(
        label=get_text("subject_name", current_language),
        expand=True,
    )

    # Function to add a new subject
    def add_new_subject(_):
        if not class_dropdown.value:
            page.app_state.show_error(get_text("please_select_class", current_language))
            return

        if not subject_name_field.value:
            subject_name_field.error_text = get_text("please_enter_subject_name", current_language)
            page.update()
            return

        # Get current teacher ID
        current_user = getattr(page.app_state, 'current_user', None)
        teacher_id = current_user['id'] if current_user and current_user['role'] == 'teacher' else None

        # Create the subject
        success = create_class_subject(
            class_dropdown.value,
            subject_name_field.value,
            "",  # Description
            "",  # Teacher name
            teacher_id  # Teacher ID
        )

        if success:
            # Store the subject name before clearing the field
            created_subject_name = subject_name_field.value

            # Clear the fields
            subject_name_field.value = ""
            subject_name_field.error_text = ""

            # Show success message with the stored subject name
            page.app_state.show_success(f"{get_text('subject', current_language)} '{created_subject_name}' {get_text('created_successfully', current_language)}")

            # Refresh the subject cards with current filters
            search_query = search_field.value if search_field.value else ""
            class_filter_value = class_filter_dropdown.value if class_filter_dropdown.value else "all"
            refresh_subject_cards(search_query, class_filter_value)
        else:
            page.app_state.show_error(f"{get_text('failed_to_create_subject', current_language)} '{subject_name_field.value}'")

    # Create add subject button with modern styling
    add_subject_button = ft.ElevatedButton(
        get_text("add", current_language),
        icon=ICON_ADD,
        on_click=add_new_subject,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.ORANGE_600,
            color=ft.Colors.WHITE,
            shape=ft.RoundedRectangleBorder(radius=12),
            padding=ft.padding.symmetric(horizontal=20, vertical=12),
            shadow_color=ft.Colors.ORANGE_200,
            elevation=2
        ),
    )

    # Initialize the subject cards with all subjects
    refresh_subject_cards()

    # Modern add subject form with enhanced styling
    add_subject_form = ft.Container(
        content=ft.Column([
            ft.Text(
                get_text("add_new_subject", current_language),
                size=18,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.ON_SURFACE
            ),
            ft.Row([subject_name_field, add_subject_button], spacing=12),
            ft.Row([class_dropdown], spacing=12),
        ], spacing=12),
        width=page.width*0.9 if is_mobile else 500,
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20),
        bgcolor=ft.Colors.SURFACE,
        border_radius=ft.border_radius.all(16),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=8,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 2)
        ),
    )

    # Modern content layout with welcome section
    content = [
        welcome_section,
        filter_section,
        ft.Container(
            content=add_subject_form,
            alignment=ft.alignment.center,
        ),
        ft.Container(
            content=subject_cards_container,
            alignment=ft.alignment.center,
        )
    ]

    return create_page_layout(
        page,
        "",  # Empty title since we have the welcome section
        content
    )

def show_edit_subject_dialog(page, subject_id, subject_name, teacher_name):
    """
    Show dialog to edit a subject.

    Args:
        page: The Flet page object
        subject_id: The subject ID
        subject_name: The subject name
        teacher_name: The teacher name
    """
    # Get current language
    current_language = getattr(page, 'language', 'en')

    # Create text fields for editing
    new_name_field = ft.TextField(
        label=get_text("subject_name", current_language),
        value=subject_name,
        width=300,
        autofocus=True
    )

    new_teacher_field = ft.TextField(
        label=get_text("teacher_name", current_language),
        value=teacher_name or "",
        width=300
    )

    # Function to handle update
    def do_update():
        if not new_name_field.value:
            new_name_field.error_text = get_text("please_enter_subject_name", current_language)
            page.update()
            return

        # Update the subject
        success = update_class_subject(
            subject_id,
            new_name_field.value,
            None,  # Description
            new_teacher_field.value
        )

        if success:
            # Close the dialog first
            page.app_state.close_dialog()

            page.app_state.show_success(get_text("subject_updated_successfully", current_language))
            # Refresh the view immediately
            page.app_state.refresh_current_view()
        else:
            page.app_state.show_error(get_text("failed_to_update_subject", current_language))

    # Create form controls
    form_controls = [
        ft.Text(get_text("edit_subject_details", current_language)),
        new_name_field,
        new_teacher_field,
    ]

    # Create and show dialog
    dialog = create_form_dialog(
        page,
        f"{get_text('edit_subject', current_language)}: {subject_name}",
        form_controls,
        do_update,
        get_text("update", current_language),
        get_text("cancel", current_language)
    )

    show_dialog(page, dialog)

def show_delete_subject_dialog(page, subject_id, subject_name):
    """
    Show dialog to confirm subject deletion.

    Args:
        page: The Flet page object
        subject_id: The subject ID
        subject_name: The subject name
    """
    # Get current language
    current_language = getattr(page, 'language', 'en')

    # Function to handle deletion
    def do_delete():
        # Delete the subject
        success = delete_class_subject(subject_id)
        if success:
            # Close the dialog first
            page.app_state.close_dialog()

            page.app_state.show_success(f"{get_text('subject', current_language)} '{subject_name}' {get_text('deleted', current_language)}")
            # Refresh the view immediately
            page.app_state.refresh_current_view()
        else:
            page.app_state.show_error(get_text("failed_to_delete_subject", current_language))

    # Create and show dialog
    dialog = create_confirmation_dialog(
        page,
        f"{get_text('delete_subject', current_language)}: {subject_name}",
        get_text("delete_subject_confirmation", current_language),
        do_delete,
        get_text("delete", current_language),
        get_text("cancel", current_language),
        is_destructive=True
    )

    show_dialog(page, dialog)


def show_subject_attendance_history(page: ft.Page, subject_id, subject_name, class_name):
    """
    Show attendance history for a specific subject in a dialog.

    Args:
        page: The Flet page object
        subject_id: The subject ID
        subject_name: The subject name
        class_name: The class name
    """
    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # State variables
    attendance_records = []
    filtered_records = []

    # Create date picker
    date_picker = ft.DatePicker(
        first_date=datetime(2020, 1, 1),
        last_date=datetime(2030, 12, 31),
        on_change=lambda _: load_attendance_records()
    )

    # Add date picker to page
    page.overlay.append(date_picker)

    date_filter = ft.ElevatedButton(
        text=get_text("select_date", current_language),
        icon=ft.Icons.CALENDAR_TODAY,
        width=180 if not is_mobile else None,
        expand=is_mobile,
        on_click=lambda _: page.open(date_picker),
        style=ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=12),
            bgcolor=ft.Colors.WHITE,
            color=ft.Colors.BLUE_600,
            elevation=0,
            side=ft.BorderSide(1, ft.Colors.BLUE_200),
            padding=ft.padding.symmetric(horizontal=16, vertical=12)
        )
    )

    search_field = ft.TextField(
        hint_text="Rechercher un étudiant...",
        prefix_icon=ICON_SEARCH,
        width=220 if not is_mobile else None,
        expand=is_mobile,
        dense=True,
        content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
        border_radius=12,
        border_color=ft.Colors.BLUE_200,
        focused_border_color=ft.Colors.BLUE_600,
        bgcolor=ft.Colors.WHITE
    )

    # Records container
    records_container = ft.Column(spacing=10)

    def load_attendance_records():
        nonlocal attendance_records, filtered_records

        # Get selected date from date picker
        selected_date = None
        if date_picker.value:
            selected_date = date_picker.value.strftime('%Y-%m-%d')
            # Update button text to show selected date
            date_filter.text = selected_date

        # Load records for this specific subject and date
        attendance_records = get_attendance_records(
            subject_id=subject_id,
            date_from=selected_date,
            date_to=selected_date
        )

        # Apply search filter
        search_term = search_field.value.lower() if search_field.value else ""
        if search_term:
            filtered_records = [r for r in attendance_records if search_term in r['student_name'].lower()]
        else:
            filtered_records = attendance_records

        display_records()

    def display_records():
        records_container.controls.clear()

        if not filtered_records:
            # Modern empty state
            empty_state = ft.Container(
                content=ft.Column([
                    ft.Icon(
                        ft.Icons.CALENDAR_TODAY_OUTLINED,
                        size=64,
                        color=ft.Colors.BLUE_GREY_300
                    ),
                    ft.Text(
                        "Aucune donnée de présence",
                        size=18,
                        weight=ft.FontWeight.W_500,
                        text_align=ft.TextAlign.CENTER,
                        color=ft.Colors.BLUE_GREY_400
                    ),
                    ft.Text(
                        "Sélectionnez une date pour afficher les présences",
                        size=14,
                        text_align=ft.TextAlign.CENTER,
                        color=ft.Colors.BLUE_GREY_300
                    )
                ], spacing=16, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(40),
                alignment=ft.alignment.center
            )
            records_container.controls.append(empty_state)
        else:
            # Group records by date
            records_by_date = {}
            for record in filtered_records:
                date = record['date']
                if date not in records_by_date:
                    records_by_date[date] = []
                records_by_date[date].append(record)

            # Sort dates (newest first)
            sorted_dates = sorted(records_by_date.keys(), reverse=True)

            # Create modern cards for each date
            for date in sorted_dates:
                date_records = records_by_date[date]
                records_container.controls.append(create_modern_date_card(date, date_records))

        page.update()

    def create_modern_date_card(date, records):
        try:
            date_obj = datetime.strptime(date, '%Y-%m-%d')
            formatted_date = date_obj.strftime('%d %B %Y')
            day_name = date_obj.strftime('%A')
        except:
            formatted_date = date
            day_name = ""

        present_count = sum(1 for r in records if r['status'])
        absent_count = len(records) - present_count
        total_count = len(records)
        attendance_rate = (present_count / total_count * 100) if total_count > 0 else 0

        # Modern date header with statistics
        date_header = ft.Container(
            content=ft.Row([
                ft.Column([
                    ft.Text(
                        formatted_date,
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.ON_SURFACE
                    ),
                    ft.Text(
                        day_name,
                        size=12,
                        color=ft.Colors.BLUE_GREY_600
                    )
                ], spacing=2),
                ft.Container(expand=True),
                ft.Row([
                    # Attendance rate badge
                    ft.Container(
                        content=ft.Text(
                            f"{attendance_rate:.0f}%",
                            size=16,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.WHITE
                        ),
                        bgcolor=ft.Colors.GREEN_600 if attendance_rate >= 80 else ft.Colors.ORANGE_600 if attendance_rate >= 60 else ft.Colors.RED_600,
                        padding=ft.padding.symmetric(horizontal=12, vertical=6),
                        border_radius=20
                    ),
                    # Present count
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN_600, size=16),
                            ft.Text(str(present_count), size=14, weight=ft.FontWeight.W_500)
                        ], spacing=4),
                        padding=ft.padding.symmetric(horizontal=8, vertical=4),
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=12
                    ),
                    # Absent count
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.CANCEL, color=ft.Colors.RED_600, size=16),
                            ft.Text(str(absent_count), size=14, weight=ft.FontWeight.W_500)
                        ], spacing=4),
                        padding=ft.padding.symmetric(horizontal=8, vertical=4),
                        bgcolor=ft.Colors.RED_50,
                        border_radius=12
                    )
                ], spacing=8)
            ]),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.BLUE_50,
            border_radius=ft.border_radius.only(top_left=12, top_right=12)
        )

        # Modern student list
        student_list = []
        for record in records:
            status_color = ft.Colors.GREEN_600 if record['status'] else ft.Colors.RED_600
            status_bg = ft.Colors.GREEN_50 if record['status'] else ft.Colors.RED_50
            status_text = "Présent" if record['status'] else "Absent"

            student_card = ft.Container(
                content=ft.Row([
                    ft.Text(
                        record['student_name'],
                        size=14,
                        weight=ft.FontWeight.W_500,
                        expand=True
                    ),
                    ft.Text(
                        record['time'],
                        size=12,
                        color=ft.Colors.BLUE_GREY_600
                    ),
                    ft.Container(
                        content=ft.Text(
                            status_text,
                            size=12,
                            weight=ft.FontWeight.W_500,
                            color=status_color
                        ),
                        bgcolor=status_bg,
                        padding=ft.padding.symmetric(horizontal=8, vertical=4),
                        border_radius=8
                    )
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                padding=ft.padding.symmetric(horizontal=16, vertical=12),
                border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.BLUE_GREY_100))
            )
            student_list.append(student_card)

        return ft.Container(
            content=ft.Column([
                date_header,
                ft.Column(student_list, spacing=0)
            ], spacing=0),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            margin=ft.margin.only(bottom=16),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=6,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )

    def on_filter_change(_):
        load_attendance_records()

    def on_search_change(_):
        load_attendance_records()

    def clear_filters(_):
        date_picker.value = None
        date_filter.text = get_text("select_date", current_language)
        search_field.value = ""
        load_attendance_records()

    # Set up event handlers
    search_field.on_change = on_search_change

    # Modern, simplified filter section
    filter_section = ft.Container(
        content=ft.Row([
            # Left side - Title and class info
            ft.Column([
                ft.Text(
                    subject_name,
                    size=20,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.ON_SURFACE
                ),
                ft.Text(
                    f"Classe {class_name}",
                    size=14,
                    color=ft.Colors.BLUE_600
                )
            ], spacing=4),
            ft.Container(expand=True),
            # Right side - Filters in a clean row
            ft.Row([
                date_filter,
                search_field,
                ft.IconButton(
                    icon=ICON_CLEAR,
                    tooltip="Effacer les filtres",
                    on_click=clear_filters,
                    style=ft.ButtonStyle(
                        shape=ft.CircleBorder(),
                        bgcolor=ft.Colors.BLUE_GREY_50,
                        color=ft.Colors.BLUE_GREY_600
                    )
                )
            ], spacing=8)
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN) if not is_mobile else ft.Column([
            ft.Text(
                subject_name,
                size=18,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.ON_SURFACE
            ),
            ft.Text(
                f"Classe {class_name}",
                size=12,
                color=ft.Colors.BLUE_600
            ),
            ft.Row([date_filter, search_field], spacing=8),
            ft.IconButton(
                icon=ICON_CLEAR,
                tooltip="Effacer les filtres",
                on_click=clear_filters
            )
        ], spacing=8),
        padding=ft.padding.symmetric(horizontal=20, vertical=16),
        bgcolor=ft.Colors.WHITE,
        border_radius=12,
        margin=ft.margin.only(bottom=20),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=4,
            color=ft.Colors.with_opacity(0.08, ft.Colors.BLACK),
            offset=ft.Offset(0, 1)
        )
    )

    # Modern records section
    records_section = ft.Container(
        content=ft.Column([records_container], scroll=ft.ScrollMode.AUTO),
        height=450,
        padding=ft.padding.all(20),
        bgcolor=ft.Colors.BLUE_GREY_50,
        border_radius=12
    )

    # Main content
    content = ft.Column([
        filter_section,
        records_section
    ], spacing=0)

    def export_subject_csv():
        """Export subject attendance records to CSV"""
        if not filtered_records:
            page.snack_bar = ft.SnackBar(
                content=ft.Text("Aucune donnée à exporter"),
                action="OK"
            )
            page.snack_bar.open = True
            page.update()
            return

        import csv
        import os
        from datetime import datetime

        # Create CSV content
        csv_content = []
        csv_content.append(['Student Name', 'Class', 'Subject', 'Date', 'Time', 'Status'])

        for record in filtered_records:
            status_text = 'Present' if record['status'] else 'Absent'
            csv_content.append([
                record['student_name'],
                record['class_name'],
                record['subject_name'],
                record['date'],
                record['time'],
                status_text
            ])

        # Create filename with timestamp and subject name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_subject_name = "".join(c for c in subject_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"attendance_{safe_subject_name}_{timestamp}.csv"

        # Save to Downloads folder or current directory
        downloads_path = os.path.expanduser("~/Downloads")
        if os.path.exists(downloads_path):
            file_path = os.path.join(downloads_path, filename)
        else:
            file_path = filename

        try:
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerows(csv_content)

            # Show success message
            page.snack_bar = ft.SnackBar(
                content=ft.Text(f"Données exportées vers: {file_path}"),
                action="OK"
            )
            page.snack_bar.open = True
            page.update()
        except Exception as e:
            # Show error message
            page.snack_bar = ft.SnackBar(
                content=ft.Text(f"Erreur lors de l'export: {str(e)}"),
                action="OK"
            )
            page.snack_bar.open = True
            page.update()

    # Initialize data
    load_attendance_records()

    # Create modern dialog
    dialog = ft.AlertDialog(
        title=ft.Row([
            ft.Icon(ft.Icons.HISTORY, color=ft.Colors.BLUE_600, size=24),
            ft.Text(
                "Historique des Présences",
                size=20,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.ON_SURFACE
            )
        ], spacing=8),
        content=ft.Container(
            content=content,
            width=800 if not is_mobile else page.width * 0.95,
            height=550
        ),
        actions=[
            ft.ElevatedButton(
                "Exporter CSV",
                on_click=lambda _: export_subject_csv(),
                icon=ft.Icons.DOWNLOAD,
                style=ft.ButtonStyle(
                    shape=ft.RoundedRectangleBorder(radius=8),
                    bgcolor=ft.Colors.GREEN_600,
                    color=ft.Colors.WHITE
                )
            ),
            ft.TextButton(
                "Fermer",
                on_click=lambda _: page.app_state.close_dialog(),
                style=ft.ButtonStyle(
                    color=ft.Colors.BLUE_600,
                    text_style=ft.TextStyle(size=14, weight=ft.FontWeight.W_500)
                )
            )
        ],
        actions_alignment=ft.MainAxisAlignment.END,
        shape=ft.RoundedRectangleBorder(radius=16)
    )

    show_dialog(page, dialog)