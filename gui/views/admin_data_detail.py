"""
Individual data detail views for admin.
"""
import flet as ft
import sqlite3
from gui.components.admin_layout import create_admin_page_layout
from facial_recognition_system.config import Config
from gui.config.constants import ROUTE_LOGIN

def create_data_table(title: str, data: list, columns: list, icon: str = ft.Icons.TABLE_CHART, page=None):
    """Create a modern data table for a specific data type."""
    is_mobile = getattr(page, 'is_mobile', False) if page else False

    if not data:
        return ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Icon(
                        ft.Icons.INBOX,
                        size=48,
                        color=ft.Colors.BLUE_300
                    ),
                    width=96,
                    height=96,
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=48,
                    alignment=ft.alignment.center
                ),
                ft.Text(
                    f"Aucune donnée {title.lower()}",
                    size=18,
                    color=ft.Colors.BLUE_900,
                    text_align=ft.TextAlign.CENTER,
                    weight=ft.FontWeight.BOLD
                ),
                ft.Text(
                    f"Les données {title.lower()} apparaîtront ici",
                    size=14,
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                )
            ], spacing=16, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            alignment=ft.alignment.center,
            padding=ft.padding.all(48),
            bgcolor=ft.Colors.WHITE,
            border_radius=ft.border_radius.all(20),
            margin=ft.margin.only(bottom=24),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLUE_600),
                offset=ft.Offset(0, 4)
            ),
            border=ft.border.all(1, ft.Colors.BLUE_100)
        )

    # Create table rows with modern styling
    rows = []
    for i, item in enumerate(data):
        cells = []
        for col in columns:
            value = str(item.get(col, ''))
            if len(value) > 30:
                value = value[:27] + "..."
            cells.append(ft.DataCell(
                ft.Text(
                    value,
                    size=13,
                    color=ft.Colors.GREY_800,
                    weight=ft.FontWeight.W_400
                )
            ))

        # Alternate row colors for better readability
        row_color = ft.Colors.BLUE_50 if i % 2 == 0 else ft.Colors.WHITE
        rows.append(ft.DataRow(cells=cells, color=row_color))

    # Create table columns with modern styling
    table_columns = [
        ft.DataColumn(
            ft.Text(
                col.replace('_', ' ').title(),
                weight=ft.FontWeight.BOLD,
                size=14,
                color=ft.Colors.BLUE_900
            )
        ) for col in columns
    ]

    return ft.Container(
        content=ft.Column([
            # Header with icon and title
            ft.Row([
                ft.Container(
                    content=ft.Icon(
                        icon,
                        size=24,
                        color=ft.Colors.BLUE_600
                    ),
                    width=40,
                    height=40,
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=20,
                    alignment=ft.alignment.center
                ),
                ft.Column([
                    ft.Text(
                        title,
                        size=20,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.BLUE_900
                    ),
                    ft.Text(
                        f"{len(data)} élément{'s' if len(data) > 1 else ''} au total",
                        size=14,
                        color=ft.Colors.GREY_600
                    )
                ], spacing=2, expand=True)
            ], spacing=12, alignment=ft.MainAxisAlignment.START),

            ft.Container(height=16),

            # Modern data table
            ft.Container(
                content=ft.DataTable(
                    columns=table_columns,
                    rows=rows,
                    border=ft.border.all(1, ft.Colors.BLUE_100),
                    border_radius=12,
                    vertical_lines=ft.BorderSide(1, ft.Colors.BLUE_50),
                    horizontal_lines=ft.BorderSide(1, ft.Colors.BLUE_50),
                    heading_row_color=ft.Colors.BLUE_600,
                    heading_row_height=56,
                    show_checkbox_column=False,
                ),
                bgcolor=ft.Colors.WHITE,
                border_radius=12,
                padding=ft.padding.all(16),
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=8,
                    color=ft.Colors.with_opacity(0.1, ft.Colors.BLUE_600),
                    offset=ft.Offset(0, 4)
                ),
            )
        ], spacing=0),
        width=page.width*0.95 if is_mobile else None,
        padding=ft.padding.all(24),
        margin=ft.margin.only(bottom=24),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(20),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=12,
            color=ft.Colors.with_opacity(0.08, ft.Colors.BLUE_600),
            offset=ft.Offset(0, 6)
        ),
        border=ft.border.all(1, ft.Colors.BLUE_100)
    )

def get_data_by_type(data_type: str):
    """Get specific data from the database."""
    try:
        with sqlite3.connect(Config.get_db_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            if data_type == 'classes':
                cursor.execute("SELECT * FROM classes ORDER BY name")
                return [dict(row) for row in cursor.fetchall()]

            elif data_type == 'students':
                cursor.execute("""
                    SELECT e.*, c.name as class_name
                    FROM etudiants e
                    LEFT JOIN classes c ON e.class_id = c.id
                    ORDER BY e.name
                """)
                return [dict(row) for row in cursor.fetchall()]

            elif data_type == 'subjects':
                cursor.execute("""
                    SELECT m.*, c.name as class_name
                    FROM matieres m
                    LEFT JOIN classes c ON m.class_id = c.id
                    ORDER BY m.name
                """)
                return [dict(row) for row in cursor.fetchall()]

            elif data_type == 'quizzes':
                cursor.execute("""
                    SELECT q.*, c.name as class_name, m.name as subject_name
                    FROM quiz q
                    LEFT JOIN classes c ON q.class_id = c.id
                    LEFT JOIN matieres m ON q.subject_id = m.id
                    ORDER BY q.created_at DESC
                """)
                return [dict(row) for row in cursor.fetchall()]

            elif data_type == 'attendance':
                cursor.execute("""
                    SELECT p.*, e.name as student_name, c.name as class_name, m.name as subject_name
                    FROM presences p
                    LEFT JOIN etudiants e ON p.student_id = e.id
                    LEFT JOIN classes c ON p.class_id = c.id
                    LEFT JOIN matieres m ON p.subject_id = m.id
                    ORDER BY p.date DESC, p.time DESC
                    LIMIT 200
                """)
                return [dict(row) for row in cursor.fetchall()]

            return []

    except Exception as e:
        print(f"❌ Failed to get {data_type} data: {e}")
        return []

def create_admin_classes_view(page: ft.Page):
    """Create the admin classes detail view with students."""
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/classes", controls=[])

    # Get classes and students data
    classes_data = get_data_by_type('classes')
    students_data = get_data_by_type('students')

    content = [
        create_data_table("Classes", classes_data, ['name', 'description', 'created_at'], ft.Icons.SCHOOL, page),
        create_data_table("Étudiants", students_data, ['name', 'class_name', 'created_at'], ft.Icons.PEOPLE, page)
    ]

    return create_admin_page_layout(
        page,
        "Classes et Étudiants",
        content
    )

def create_admin_subjects_view(page: ft.Page):
    """Create the admin subjects detail view with quizzes and attendance."""
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/subjects", controls=[])

    # Get subjects, quizzes, and attendance data
    subjects_data = get_data_by_type('subjects')
    quizzes_data = get_data_by_type('quizzes')
    attendance_data = get_data_by_type('attendance')

    content = [
        create_data_table("Matières", subjects_data, ['name', 'class_name', 'description', 'created_at'], ft.Icons.BOOK, page),
        create_data_table("Quiz", quizzes_data, ['title', 'class_name', 'subject_name', 'created_at'], ft.Icons.QUIZ, page),
        create_data_table("Présences", attendance_data, ['student_name', 'class_name', 'subject_name', 'status', 'date', 'time'], ft.Icons.CHECK_CIRCLE, page)
    ]

    return create_admin_page_layout(
        page,
        "Matières, Quiz et Présences",
        content
    )
