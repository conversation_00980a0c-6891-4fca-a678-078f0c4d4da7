"""
Theme management for the application.
"""
import flet as ft
from gui.config.constants import ICON_LIGHT_MODE, ICON_DARK_MODE

def toggle_theme(page: ft.Page, icon_button=None):
    """
    Toggle between light and dark theme.
    
    Args:
        page: The Flet page object
        icon_button: The icon button to update (optional)
    """
    # Toggle theme mode
    is_light_mode = page.theme_mode == ft.ThemeMode.LIGHT
    page.theme_mode = ft.ThemeMode.DARK if is_light_mode else ft.ThemeMode.LIGHT
    
    # Update icon if provided
    if icon_button:
        if page.theme_mode == ft.ThemeMode.DARK:
            icon_button.icon = ICON_LIGHT_MODE
            icon_button.tooltip = "Switch to light mode"
        else:
            icon_button.icon = ICON_DARK_MODE
            icon_button.tooltip = "Switch to dark mode"
    
    page.update()

def get_theme_icon_button(page: ft.Page):
    """
    Create a theme toggle icon button.
    
    Args:
        page: The Flet page object
        
    Returns:
        ft.IconButton: The theme toggle icon button
    """
    is_dark_mode = page.theme_mode == ft.ThemeMode.DARK
    icon = ICON_LIGHT_MODE if is_dark_mode else ICON_DARK_MODE
    tooltip = "Switch to light mode" if is_dark_mode else "Switch to dark mode"
    
    theme_icon = ft.IconButton(
        icon=icon,
        tooltip=tooltip,
        on_click=lambda _: toggle_theme(page, theme_icon)
    )
    
    return theme_icon
