import qrcode
import flet as ft
import io
import threading
import time
import socket
import os
from PIL import Image
from gui.utils.network import get_local_ip

def shorten_url(long_url, verify_ssl=True):
    import requests
    try:
        response = requests.get(
            f"https://tinyurl.com/api-create.php?url={long_url}",
            timeout=5,
            verify=verify_ssl
        )
        if response.status_code == 200:
            return response.text
        else:
            return long_url
    except requests.exceptions.SSLError:
        if verify_ssl:
            return shorten_url(long_url, verify_ssl=False)
        return long_url
    except Exception:
        return long_url

def generate_enrollment_url(class_id, class_name, host=None, port=None, shorten=True, verify_ssl=True):
    from facial_recognition_system.config import Config
    import urllib.parse
    if port is None:
        port = Config.ENROLLMENT_PORT
    if host is None:
        host = get_local_ip()
    encoded_class_name = urllib.parse.quote(class_name)
    full_url = f"http://{host}:{port}/?class_id={class_id}&class_name={encoded_class_name}"
    if shorten:
        return shorten_url(full_url, verify_ssl=verify_ssl)
    else:
        return full_url

def generate_quiz_url(quiz_id, quiz_title, host=None, port=None, shorten=True, verify_ssl=True):
    from facial_recognition_system.config import Config
    import urllib.parse
    if port is None:
        port = Config.QUIZ_PORT if hasattr(Config, 'QUIZ_PORT') else 5001
    if host is None:
        host = get_local_ip()
    encoded_quiz_title = urllib.parse.quote(quiz_title)
    url = f"http://{host}:{port}/quiz?quiz_id={quiz_id}&quiz_title={encoded_quiz_title}"
    if shorten:
        return shorten_url(url, verify_ssl=verify_ssl)
    else:
        return url

def create_qr_code_image(data, size=200):
    import base64
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_M,
        box_size=10,
        border=4,
    )
    qr.add_data(data)
    qr.make(fit=True)
    img = qr.make_image(fill_color="black", back_color="white")
    img = img.resize((size, size), Image.LANCZOS)
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    img_base64 = base64.b64encode(img_bytes.getvalue()).decode('utf-8')
    return ft.Image(
        src=f"data:image/png;base64,{img_base64}",
        width=size,
        height=size,
        fit=ft.ImageFit.CONTAIN,
        expand=True,
    )

_enrollment_server_thread = None
_enrollment_server_running = False
_quiz_server_thread = None
_quiz_server_running = False

def start_enrollment_server(upload_dir="student_uploads"):
    global _enrollment_server_thread, _enrollment_server_running
    os.makedirs(upload_dir, exist_ok=True)
    from facial_recognition_system.config import Config
    enrollment_port = Config.ENROLLMENT_PORT
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.connect(('localhost', enrollment_port))
            _enrollment_server_running = True
            return _enrollment_server_thread, enrollment_port
    except ConnectionRefusedError:
        def run_server_thread():
            from gui.services.enrollment_service import run_server
            run_server(host='0.0.0.0', port=enrollment_port)
        _enrollment_server_thread = threading.Thread(target=run_server_thread, daemon=True)
        _enrollment_server_thread.start()
        _enrollment_server_running = True
        time.sleep(1)
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.connect(('localhost', enrollment_port))
        except ConnectionRefusedError:
            _enrollment_server_running = False
        return _enrollment_server_thread, enrollment_port

def stop_enrollment_server():
    global _enrollment_server_running
    if not _enrollment_server_running:
        return False
    _enrollment_server_running = False
    return True

def is_enrollment_server_running():
    global _enrollment_server_running
    return _enrollment_server_running

def start_quiz_server():
    global _quiz_server_thread, _quiz_server_running
    from facial_recognition_system.config import Config
    quiz_port = Config.QUIZ_PORT if hasattr(Config, 'QUIZ_PORT') else 5001
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.connect(('localhost', quiz_port))
            _quiz_server_running = True
            return _quiz_server_thread, quiz_port
    except ConnectionRefusedError:
        def run_server_thread():
            from quiz_management.services.quiz_taking_service import run_server
            run_server(host='0.0.0.0', port=quiz_port)
        _quiz_server_thread = threading.Thread(target=run_server_thread, daemon=True)
        _quiz_server_thread.start()
        _quiz_server_running = True
        time.sleep(1)
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.connect(('localhost', quiz_port))
        except ConnectionRefusedError:
            _quiz_server_running = False
        return _quiz_server_thread, quiz_port

def stop_quiz_server():
    global _quiz_server_running
    if not _quiz_server_running:
        return False
    _quiz_server_running = False
    return True

def is_quiz_server_running():
    global _quiz_server_running
    return _quiz_server_running
