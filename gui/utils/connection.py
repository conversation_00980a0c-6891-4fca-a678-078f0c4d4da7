
import time
from facial_recognition_system.local_database import get_connection

# Cache connection status to avoid frequent database checks
_connection_cache = {
    'status': None,
    'last_check': 0,
    'cache_duration': 5  # Cache for 5 seconds
}

def check_connection(page):
    """Check database connection with caching to improve performance."""
    current_time = time.time()

    # Use cached result if recent
    if (_connection_cache['status'] is not None and
        current_time - _connection_cache['last_check'] < _connection_cache['cache_duration']):
        return _connection_cache['status']

    try:
        # Try to get a connection to the local database
        conn = get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT 1")

        # Update cache with success
        _connection_cache['status'] = True
        _connection_cache['last_check'] = current_time
        return True
    except Exception as e:
        # Update cache with failure
        _connection_cache['status'] = False
        _connection_cache['last_check'] = current_time

        # Database error, show error dialog
        page.app_state.show_connection_error_dialog(f"Database error: {str(e)}")
        return False
