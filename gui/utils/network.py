import socket

def get_local_ip():
    """Get local IP address with timeout and fallback mechanisms."""
    try:
        # Try with timeout to avoid hanging
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.settimeout(2)  # 2 second timeout
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        # Fallback: try to get IP from hostname
        try:
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            # Avoid returning localhost if possible
            if local_ip.startswith("127."):
                # Try alternative method
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.settimeout(1)
                s.connect(("*******", 80))  # Try Cloudflare DNS as fallback
                local_ip = s.getsockname()[0]
                s.close()
                return local_ip
            return local_ip
        except Exception:
            return "127.0.0.1"
