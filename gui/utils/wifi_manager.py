"""
WiFi Manager Utility - Touch-Friendly Version

This utility provides functions for scanning and connecting to WiFi networks
using NetworkManager (nmcli) which doesn't require sudo for most operations.
"""

import subprocess
import re
import time
from typing import List, Dict, Optional
from gui.config.language import get_text, DEFAULT_LANGUAGE


class WiFiNetwork:
    """Represents a WiFi network."""

    def __init__(self, ssid: str, signal_strength: int, security: str, frequency: str = "", language=DEFAULT_LANGUAGE):
        self.ssid = ssid
        self.signal_strength = signal_strength
        self.security = security
        self.frequency = frequency
        # Check for open networks using translated strings
        open_indicators = ["Open", "--", "", get_text('open_network', language)]
        self.is_secured = security not in open_indicators


class WiFiManager:
    """Manages WiFi connections and scanning using NetworkManager."""

    def __init__(self, language=DEFAULT_LANGUAGE):
        self.language = language
        self.use_nmcli = self._check_nmcli_available()
        self.interface = self._get_wifi_interface()

    def _check_nmcli_available(self) -> bool:
        """Check if NetworkManager CLI is available."""
        try:
            result = subprocess.run(['nmcli', '--version'], capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except Exception:
            return False

    def _get_wifi_interface(self) -> Optional[str]:
        """Get the WiFi interface name."""
        try:
            if self.use_nmcli:
                # Use nmcli to get WiFi interface
                result = subprocess.run(['nmcli', 'device', 'status'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines[1:]:  # Skip header
                        parts = line.split()
                        if len(parts) >= 2 and 'wifi' in parts[1]:
                            return parts[0]

            # Fallback: try iwconfig
            result = subprocess.run(['iwconfig'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'IEEE 802.11' in line:
                        interface = line.split()[0]
                        return interface

            # Last resort: try common interface names
            common_interfaces = ['wlan0', 'wlp2s0', 'wlp3s0', 'wifi0']
            for interface in common_interfaces:
                result = subprocess.run(['iwconfig', interface], capture_output=True, text=True, timeout=2)
                if result.returncode == 0 and 'IEEE 802.11' in result.stdout:
                    return interface

        except Exception as e:
            print(f"{get_text('error_getting_wifi_interface', self.language)}: {e}")

        return None

    def scan_networks(self) -> List[WiFiNetwork]:
        """Scan for available WiFi networks using NetworkManager."""
        networks = []
        try:
            if self.use_nmcli:
                # Use nmcli to scan for networks (no sudo required)
                result = subprocess.run(['nmcli', 'device', 'wifi', 'rescan'],
                                      capture_output=True, text=True, timeout=10)
                # Rescan might fail but that's okay, continue with listing

                # List available networks
                result = subprocess.run(['nmcli', 'device', 'wifi', 'list'],
                                      capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    networks = self._parse_nmcli_results(result.stdout)
                else:
                    print(f"{get_text('nmcli_scan_failed', self.language)}: {result.stderr}")

            # Fallback to iwlist if nmcli not available
            if not networks and self.interface:
                print(get_text("trying_iwlist_fallback", self.language))
                result = subprocess.run(['iwlist', self.interface, 'scan'],
                                      capture_output=True, text=True, timeout=15)

                if result.returncode == 0:
                    networks = self._parse_iwlist_results(result.stdout)
                else:
                    print(f"{get_text('iwlist_scan_failed', self.language)}: {result.stderr}")

        except subprocess.TimeoutExpired:
            print(get_text("wifi_scan_timed_out", self.language))
        except Exception as e:
            print(f"{get_text('error_scanning_wifi', self.language)}: {e}")

        return networks

    def _parse_nmcli_results(self, scan_output: str) -> List[WiFiNetwork]:
        """Parse nmcli wifi list output."""
        networks = []
        lines = scan_output.split('\n')

        for line in lines[1:]:  # Skip header
            if not line.strip():
                continue

            # nmcli output format: SSID MODE CHAN RATE SIGNAL BARS SECURITY
            parts = line.split()
            if len(parts) >= 6:
                ssid = parts[0] if parts[0] != '--' else get_text('hidden_network', self.language)
                signal = parts[4] if parts[4].isdigit() else 0
                security = ' '.join(parts[6:]) if len(parts) > 6 else get_text('open_network', self.language)

                if ssid and ssid != get_text('hidden_network', self.language):
                    networks.append(WiFiNetwork(
                        ssid=ssid,
                        signal_strength=int(signal) if str(signal).isdigit() else 0,
                        security=security if security else get_text('open_network', self.language),
                        language=self.language
                    ))

        # Remove duplicates and sort by signal strength
        unique_networks = {}
        for network in networks:
            if network.ssid not in unique_networks or network.signal_strength > unique_networks[network.ssid].signal_strength:
                unique_networks[network.ssid] = network

        return sorted(unique_networks.values(), key=lambda x: x.signal_strength, reverse=True)

    def _parse_iwlist_results(self, scan_output: str) -> List[WiFiNetwork]:
        """Parse iwlist scan output."""
        networks = []
        current_network = {}

        for line in scan_output.split('\n'):
            line = line.strip()

            # New cell (network)
            if 'Cell' in line and 'Address:' in line:
                if current_network.get('ssid'):
                    networks.append(self._create_network_from_data(current_network))
                current_network = {}

            # SSID
            elif 'ESSID:' in line:
                ssid_match = re.search(r'ESSID:"([^"]*)"', line)
                if ssid_match:
                    current_network['ssid'] = ssid_match.group(1)

            # Signal strength
            elif 'Quality=' in line:
                quality_match = re.search(r'Quality=(\d+)/(\d+)', line)
                if quality_match:
                    quality = int(quality_match.group(1))
                    max_quality = int(quality_match.group(2))
                    current_network['signal_strength'] = int((quality / max_quality) * 100)

            # Security
            elif 'Encryption key:' in line:
                if 'off' in line:
                    current_network['security'] = get_text('open_network', self.language)
                else:
                    current_network['security'] = get_text('secured_network', self.language)

            # WPA/WPA2
            elif 'IEEE 802.11i/WPA2' in line:
                current_network['security'] = get_text('wpa2_network', self.language)
            elif 'WPA Version' in line:
                current_network['security'] = get_text('wpa_network', self.language)

        # Add last network
        if current_network.get('ssid'):
            networks.append(self._create_network_from_data(current_network))

        # Remove duplicates and sort by signal strength
        unique_networks = {}
        for network in networks:
            if network.ssid not in unique_networks or network.signal_strength > unique_networks[network.ssid].signal_strength:
                unique_networks[network.ssid] = network

        return sorted(unique_networks.values(), key=lambda x: x.signal_strength, reverse=True)

    def _create_network_from_data(self, data: Dict) -> WiFiNetwork:
        """Create WiFiNetwork object from parsed data."""
        return WiFiNetwork(
            ssid=data.get('ssid', get_text('unknown_network', self.language)),
            signal_strength=data.get('signal_strength', 0),
            security=data.get('security', get_text('unknown_network', self.language)),
            language=self.language
        )

    def connect_to_network(self, ssid: str, password: str = "") -> bool:
        """Connect to a WiFi network using NetworkManager."""
        try:
            if self.use_nmcli:
                # Use nmcli to connect (no sudo required for most systems)
                if password:
                    # Secured network
                    result = subprocess.run([
                        'nmcli', 'device', 'wifi', 'connect', ssid, 'password', password
                    ], capture_output=True, text=True, timeout=30)
                else:
                    # Open network
                    result = subprocess.run([
                        'nmcli', 'device', 'wifi', 'connect', ssid
                    ], capture_output=True, text=True, timeout=30)

                if result.returncode == 0:
                    print(f"{get_text('successfully_connected_to', self.language)} {ssid}")
                    return True
                else:
                    print(f"{get_text('nmcli_connection_failed', self.language)}: {result.stderr}")
                    return False

            # Fallback to wpa_supplicant method (requires sudo)
            else:
                print(get_text("networkmanager_not_available", self.language))
                return self._connect_with_wpa_supplicant(ssid, password)

        except Exception as e:
            print(f"{get_text('error_connecting_to_wifi', self.language)}: {e}")
            return False

    def _connect_with_wpa_supplicant(self, ssid: str, password: str = "") -> bool:
        """Fallback connection method using wpa_supplicant."""
        if not self.interface:
            print(get_text("no_wifi_interface_found", self.language))
            return False

        try:
            # Create wpa_supplicant configuration
            if password:
                wpa_config = f'''
network={{
    ssid="{ssid}"
    psk="{password}"
}}
'''
            else:
                wpa_config = f'''
network={{
    ssid="{ssid}"
    key_mgmt=NONE
}}
'''

            # Write temporary config file
            config_file = '/tmp/wpa_temp.conf'
            with open(config_file, 'w') as f:
                f.write(wpa_config)

            # This method requires sudo - will prompt user
            print(get_text("method_requires_admin", self.language))

            # Kill existing wpa_supplicant processes
            subprocess.run(['sudo', 'pkill', 'wpa_supplicant'], capture_output=True)
            time.sleep(1)

            # Start wpa_supplicant
            result = subprocess.run([
                'sudo', 'wpa_supplicant', '-B', '-i', self.interface,
                '-c', config_file, '-D', 'wext'
            ], capture_output=True, text=True, timeout=10)

            if result.returncode != 0:
                print(f"{get_text('failed_to_start_wpa_supplicant', self.language)}: {result.stderr}")
                return False

            # Wait for connection
            time.sleep(3)

            # Get IP address using dhclient
            result = subprocess.run(['sudo', 'dhclient', self.interface],
                                  capture_output=True, text=True, timeout=15)

            if result.returncode != 0:
                print(f"{get_text('failed_to_get_ip_address', self.language)}: {result.stderr}")
                return False

            # Verify connection
            time.sleep(2)
            if self.is_connected():
                print(f"{get_text('successfully_connected_to', self.language)} {ssid}")
                return True
            else:
                print(f"{get_text('failed_to_connect_to', self.language)} {ssid}")
                return False

        except Exception as e:
            print(f"{get_text('error_connecting_to_wifi', self.language)}: {e}")
            return False

    def is_connected(self) -> bool:
        """Check if WiFi is connected."""
        if not self.interface:
            return False

        try:
            result = subprocess.run(['iwconfig', self.interface],
                                  capture_output=True, text=True, timeout=5)
            return 'Access Point:' in result.stdout and 'Not-Associated' not in result.stdout
        except Exception:
            return False

    def get_current_network(self) -> Optional[str]:
        """Get the currently connected network SSID."""
        if not self.interface:
            return None

        try:
            result = subprocess.run(['iwconfig', self.interface],
                                  capture_output=True, text=True, timeout=5)

            ssid_match = re.search(r'ESSID:"([^"]*)"', result.stdout)
            if ssid_match:
                return ssid_match.group(1)
        except Exception:
            pass

        return None


def restart_application(language=DEFAULT_LANGUAGE):
    """Restart the application after WiFi change."""
    import os
    import sys

    print(get_text("restarting_application", language))

    # Get the current script path
    script_path = os.path.abspath(sys.argv[0])

    # Restart the application
    os.execv(sys.executable, [sys.executable, script_path] + sys.argv[1:])


# Global instance
_wifi_manager = None

def get_wifi_manager(language=DEFAULT_LANGUAGE) -> WiFiManager:
    """Get the global WiFi manager instance."""
    global _wifi_manager
    if _wifi_manager is None:
        _wifi_manager = WiFiManager(language)
    return _wifi_manager
