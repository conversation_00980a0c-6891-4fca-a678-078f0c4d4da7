"""
Service for dashboard statistics and data.
"""
from gui.services.class_service import get_existing_classes
from facial_recognition_system.database import get_existing_records
from gui.services.subject_service import get_class_subjects
from quiz_management.services.quiz_service import get_quizzes

def get_dashboard_statistics():
    """Get statistics for the dashboard

    Returns:
        dict: A dictionary of statistics
    """
    try:
        # Get basic counts
        classes = get_existing_classes()
        students = get_existing_records()
        subjects = get_class_subjects()
        quizzes = get_quizzes()

        # Calculate statistics
        stats = {
            "classes_count": len(classes),
            "students_count": len(students),
            "subjects_count": len(subjects),
            "quizzes_count": len(quizzes)
        }

        return stats
    except Exception:
        # Return default values to prevent UI errors
        return {
            "classes_count": 0,
            "students_count": 0,
            "subjects_count": 0,
            "quizzes_count": 0
        }
