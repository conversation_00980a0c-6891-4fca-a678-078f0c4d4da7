import cv2
import threading
import time
import face_recognition
import numpy as np
from flask import Flask, Response, render_template_string, request, jsonify
from facial_recognition_system.local_database import get_connection, get_students_in_class
import json

from hardware.JetsonCamera import Camera
from hardware.Focuser import Focuser
from gui.config.language import get_text, get_current_language

app = Flask(__name__)

# Global variables
camera = None
focuser = None
output_frame = None
lock = threading.Lock()
streaming_active = False
current_class_id = None
current_class_name = None
current_subject_id = None
current_subject_name = None
recognized_students = set()
known_face_encodings = []
known_face_names = []
manual_attendance = {}  # Dictionary to store manual attendance overrides
all_class_students = []  # List of all students in the current class

# Fixed I2C bus to 8 and focus value
I2C_BUS = 8
FOCUS_VALUE = 200
current_language = get_current_language()

def initialize_camera():
    global camera, focuser, current_language
    try:
        print(f"🔍 {get_text('initializing_jetson_camera', current_language)}")

        # Initialize camera like in FocuserExample.py
        camera = Camera()
        print(f"✅ {get_text('camera_object_created', current_language)}")

        # Initialize focuser with fixed I2C bus 8
        print(f"🔍 {get_text('initializing_focuser', current_language).format(bus=I2C_BUS)}")
        focuser = Focuser(I2C_BUS)
        print(f"✅ {get_text('focuser_initialized', current_language)}")

        # Set fixed focus value
        print(f"🔍 {get_text('setting_focus', current_language).format(value=FOCUS_VALUE)}")
        focuser.set(Focuser.OPT_FOCUS, FOCUS_VALUE)

        print(f"✅ {get_text('jetson_camera_initialized', current_language)}")
        print(f"✅ {get_text('focus_set', current_language).format(value=FOCUS_VALUE, bus=I2C_BUS)}")
        return True
    except Exception as e:
        print(f"❌ {get_text('failed_to_initialize_camera', current_language)}: {e}")
        print(f"❌ {get_text('error_details', current_language)}: {type(e).__name__}: {str(e)}")
        return False

def release_camera():
    global camera, focuser, streaming_active, output_frame
    streaming_active = False
    with lock:
        output_frame = None
    if camera is not None:
        try:
            camera.close()
        except Exception:
            pass
        finally:
            camera = None
            focuser = None

def load_student_data(class_id):
    global known_face_encodings, known_face_names, all_class_students, manual_attendance
    known_face_encodings = []
    known_face_names = []

    try:
        students = get_students_in_class(class_id)
        if not students:
            return False

        all_class_students = [{'name': name, 'id': student_id} for name, student_id in students]
        manual_attendance = {name: "Absent" for name, _ in students}

        student_ids = [student_id for _, student_id in students]
        conn = get_connection()
        cursor = conn.cursor()

        placeholders = ', '.join(['?' for _ in student_ids])
        query = f"SELECT name, code FROM etudiants WHERE id IN ({placeholders})"
        cursor.execute(query, student_ids)
        rows = cursor.fetchall()

        if not rows:
            return False

        for row in rows:
            name = row['name']
            code = json.loads(row['code'])

            if isinstance(code, list):
                if code and isinstance(code[0], list):
                    for encoding in code:
                        if len(encoding) == 128:
                            known_face_encodings.append(np.array(encoding))
                            known_face_names.append(name)
                elif len(code) == 128:
                    known_face_encodings.append(np.array(code))
                    known_face_names.append(name)

        return True
    except Exception:
        return False

def generate_frames():
    """Generate frames from the camera with face recognition"""
    global output_frame, lock, streaming_active, recognized_students

    # Initialize face recognition variables
    face_locations = []
    face_encodings = []
    face_names = []
    process_this_frame = True

    while streaming_active:
        if camera is None:
            break

        try:
            frame = camera.getFrame(timeout=1000)  # 1 second timeout
            if frame is None:
                break
        except Exception:
            break

        # Flip the frame horizontally to create a mirror effect
        frame = cv2.flip(frame, 1)

        # Only process every other frame to save time
        if process_this_frame and known_face_encodings:
            # Resize frame for faster face recognition processing
            small_frame = cv2.resize(frame, (0, 0), fx=0.25, fy=0.25)

            # Convert the image from BGR color (OpenCV) to RGB color (face_recognition)
            rgb_small_frame = cv2.cvtColor(small_frame, cv2.COLOR_BGR2RGB)

            # Find all the faces and face encodings in the current frame
            face_locations = face_recognition.face_locations(rgb_small_frame)
            face_encodings = face_recognition.face_encodings(rgb_small_frame, face_locations)

            face_names = []
            for face_encoding in face_encodings:
                try:
                    # Only proceed if we have known face encodings to compare against
                    if known_face_encodings and len(known_face_encodings) > 0:
                        # See if the face is a match for the known face(s)
                        matches = face_recognition.compare_faces(known_face_encodings, face_encoding, tolerance=0.6)
                        name = "Unknown"

                        # Use the known face with the smallest distance to the new face
                        face_distances = face_recognition.face_distance(known_face_encodings, face_encoding)
                        best_match_index = np.argmin(face_distances)
                        if matches[best_match_index]:
                            name = known_face_names[best_match_index]
                            recognized_students.add(name)
                    else:
                        name = "Unknown"
                except Exception:
                    name = "Unknown"

                face_names.append(name)

        process_this_frame = not process_this_frame

        # Display the results
        for (top, right, bottom, left), name in zip(face_locations, face_names):
            # Scale back up face locations since the frame we detected in was scaled to 1/4 size
            top *= 4
            right *= 4
            bottom *= 4
            left *= 4

            color = (0, 255, 0) if name != "Unknown" else (0, 0, 255)
            cv2.rectangle(frame, (left, top), (right, bottom), color, 2)
            cv2.rectangle(frame, (left, bottom - 35), (right, bottom), color, cv2.FILLED)
            cv2.putText(frame, name, (left + 6, bottom - 6), cv2.FONT_HERSHEY_DUPLEX, 0.6, (255, 255, 255), 1)

        if current_class_name:
            cv2.rectangle(frame, (10, 10), (400, 80), (40, 44, 52), cv2.FILLED)
            cv2.putText(frame, f"Class: {current_class_name}", (20, 35), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 1)
            cv2.putText(frame, f"Students: {len(recognized_students)}/{len(set(known_face_names))}", (20, 65), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 1)

        # Acquire the lock, set the output frame, and release the lock
        with lock:
            output_frame = frame.copy()



def generate():
    """Generate the response containing the video frames"""
    global output_frame, lock

    while streaming_active:
        # Wait until the lock is acquired
        with lock:
            # Check if the output frame is available
            if output_frame is None:
                continue

            # Encode the frame in JPEG format
            (flag, encoded_image) = cv2.imencode(".jpg", output_frame)

            # Ensure the frame was successfully encoded
            if not flag:
                continue

        # Yield the output frame in the byte format
        yield(b'--frame\r\n' b'Content-Type: image/jpeg\r\n\r\n' +
              bytearray(encoded_image) + b'\r\n')

        # Sleep for a short duration
        time.sleep(0.03)

@app.route('/video_feed')
def video_feed():
    """Route for video feed"""
    return Response(generate(),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/start_stream', methods=['POST'])
def start_stream():
    """Start the video stream for a specific class"""
    global streaming_active, current_class_id, current_class_name, current_subject_id, current_subject_name, recognized_students, manual_attendance, all_class_students

    # Get class and subject information from request
    data = request.json
    class_id = data.get('class_id')
    class_name = data.get('class_name')
    subject_id = data.get('subject_id')
    subject_name = data.get('subject_name')

    if not class_id or not class_name:
        return jsonify({'success': False, 'message': 'Class information is required'}), 400

    # Initialize camera if not already initialized
    if camera is None and not initialize_camera():
        return jsonify({'success': False, 'message': 'Failed to initialize camera'}), 500

    # Load student data for the class
    if not load_student_data(class_id):
        return jsonify({'success': False, 'message': 'Failed to load student data'}), 500

    # Set current class and subject information
    current_class_id = class_id
    current_class_name = class_name
    current_subject_id = subject_id
    current_subject_name = subject_name
    recognized_students = set()
    manual_attendance = {}
    all_class_students = []

    # Start streaming if not already active
    if not streaming_active:
        streaming_active = True
        threading.Thread(target=generate_frames, daemon=True).start()

    return jsonify({'success': True, 'message': 'Stream started successfully'})

@app.route('/stop_stream', methods=['POST'])
def stop_stream():
    """Stop the video stream"""
    global streaming_active

    streaming_active = False
    release_camera()

    return jsonify({'success': True, 'message': 'Stream stopped successfully'})

@app.route('/get_recognized_students', methods=['GET'])
def get_recognized_students():
    """Get the list of recognized students"""
    global recognized_students

    return jsonify({
        'success': True,
        'recognized_students': list(recognized_students),
        'total_students': len(set(known_face_names)) if known_face_names else 0
    })

@app.route('/get_class_students', methods=['GET'])
def get_class_students():
    """Get all students in the current class with their attendance status"""
    global all_class_students, manual_attendance, recognized_students

    students_with_status = []
    for student in all_class_students:
        name = student['name']
        # Check if student is recognized by facial recognition or manually marked present
        is_present = name in recognized_students or manual_attendance.get(name) == "Present"
        students_with_status.append({
            'name': name,
            'id': student['id'],
            'status': 'Present' if is_present else 'Absent',
            'recognized': name in recognized_students,
            'manual': manual_attendance.get(name, 'Absent')
        })

    return jsonify({
        'success': True,
        'students': students_with_status
    })

@app.route('/toggle_attendance', methods=['POST'])
def toggle_attendance():
    """Toggle manual attendance for a student"""
    global manual_attendance

    data = request.json
    student_name = data.get('student_name')
    new_status = data.get('status')

    if not student_name or new_status not in ['Present', 'Absent']:
        return jsonify({'success': False, 'message': 'Invalid data'}), 400

    manual_attendance[student_name] = new_status

    return jsonify({
        'success': True,
        'message': f'Attendance updated for {student_name}',
        'student_name': student_name,
        'status': new_status
    })

@app.route('/take_attendance', methods=['POST'])
def take_attendance():
    """Capture current recognition state and update attendance"""
    global recognized_students, manual_attendance, all_class_students, streaming_active

    # Update manual attendance based on current recognition state
    for student in all_class_students:
        name = student['name']
        if name in recognized_students:
            manual_attendance[name] = "Present"

    streaming_active = False
    release_camera()

    return jsonify({
        'success': True,
        'message': 'Attendance captured from video stream',
        'recognized_count': len(recognized_students),
        'total_students': len(all_class_students)
    })

@app.route('/save_attendance', methods=['POST'])
def save_attendance():
    """Save attendance data to database"""
    global manual_attendance, current_class_id, current_subject_id

    try:
        from gui.services.attendance_service import save_attendance

        if not manual_attendance:
            return jsonify({
                'success': True,
                'message': 'No students in class - nothing to save'
            })

        class_id = int(current_class_id) if current_class_id and str(current_class_id).isdigit() else None
        subject_id = int(current_subject_id) if current_subject_id and str(current_subject_id).isdigit() else None

        success = save_attendance(
            attendance_data=manual_attendance,
            class_id=class_id,
            subject_id=subject_id
        )

        if success:
            return jsonify({
                'success': True,
                'message': 'Attendance saved successfully'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to save attendance'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error saving attendance: {str(e)}'
        }), 500

@app.route('/stream_page')
def stream_page():
    """Render the video streaming page"""
    class_id = request.args.get('class_id', '')
    class_name = request.args.get('class_name', '')
    subject_id = request.args.get('subject_id', '')
    subject_name = request.args.get('subject_name', '')
    # Start the stream immediately
    global streaming_active, current_class_id, current_class_name, current_subject_id, current_subject_name, recognized_students, manual_attendance, all_class_students

    # Set current class and subject information
    current_class_id = class_id
    current_class_name = class_name
    current_subject_id = subject_id
    current_subject_name = subject_name
    recognized_students = set()
    manual_attendance = {}
    all_class_students = []

    try:
        if camera is None:
            if not initialize_camera():
                return "Error: Failed to initialize camera. Please check your camera connection.", 500

        if not load_student_data(class_id):
            pass

        if not streaming_active:
            streaming_active = True
            threading.Thread(target=generate_frames, daemon=True).start()
    except Exception as e:
        return f"Error starting stream: {str(e)}", 500

    # HTML template for the streaming page
    html = f'''
    <!DOCTYPE html>
    <html>
    <head>
        <title>{get_text("attendance_video_stream", current_language)}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body { font-family: system-ui, sans-serif; margin: 0; padding: 20px; background: #f5f7fa; }
            .main-container { display: grid; grid-template-columns: 1fr 400px; gap: 24px; max-width: 1400px; margin: 0 auto; }
            .video-section, .attendance-section { background: white; padding: 24px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
            .attendance-section { max-height: 80vh; overflow-y: auto; }
            .video-container { margin: 24px 0; border-radius: 12px; overflow: hidden; }
            .video-feed { width: 100%; display: block; border-radius: 12px; }
            h2, h3 { color: #1a1f36; margin: 0 0 16px 0; }
            .info { background: #f8fafc; padding: 12px 16px; border-radius: 8px; margin: 8px 0; display: flex; justify-content: space-between; }
            .button { background: #ef4444; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; margin: 4px; }
            .button.primary { background: #3b82f6; }
            .button.success { background: #10b981; }
            .student-item { display: flex; justify-content: space-between; align-items: center; padding: 12px 16px; margin: 8px 0; border-radius: 8px; border: 1px solid #e5e7eb; }
            .student-item.present { background: #f0fdf4; border-color: #10b981; }
            .student-item.absent { background: #fef2f2; border-color: #ef4444; }
            .student-status { display: flex; align-items: center; gap: 8px; }
            .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; color: white; }
            .status-badge.present { background: #10b981; }
            .status-badge.absent { background: #ef4444; }
            .status-badge.recognized { background: #3b82f6; }
            .toggle-btn { padding: 6px 12px; font-size: 12px; border-radius: 6px; border: none; cursor: pointer; color: white; }
            .toggle-btn.present { background: #ef4444; }
            .toggle-btn.absent { background: #10b981; }
            .action-buttons { display: flex; gap: 12px; margin: 24px 0; }
            .loading { opacity: 0.6; pointer-events: none; }
            @media (max-width: 1024px) { .main-container { grid-template-columns: 1fr; } }
        </style>
    </head>
    <body>
        <div class="main-container">
            <!-- Video Section -->
            <div class="video-section">
                <h2>{get_text("live_attendance_stream", current_language)}</h2>
                <div class="info">
                    <span>{get_text("class_label", current_language)}</span>
                    <span id="className">{{class_name}}</span>
                </div>
                <div class="info">
                    <span>{get_text("subject_label", current_language)}</span>
                    <span id="subjectName">{{subject_name}}</span>
                </div>
                <div class="info">
                    <span>{get_text("students_recognized", current_language)}</span>
                    <span id="studentCount">0/0</span>
                </div>

                <div class="video-container">
                    <img src="{{ url_for('video_feed') }}" class="video-feed">
                </div>

                <div class="action-buttons">
                    <button id="takeAttendanceBtn" class="button primary" onclick="takeAttendance()">{get_text("take_attendance", current_language)}</button>
                    <button id="saveAttendanceBtn" class="button success" onclick="saveAttendance()" style="display: none;">{get_text("save_attendance", current_language)}</button>
                    <button id="stopBtn" class="button" onclick="stopStream()">{get_text("stop_stream", current_language)}</button>
                </div>
            </div>

            <!-- Attendance Section -->
            <div class="attendance-section">
                <h3>{get_text("student_attendance", current_language)}</h3>
                <div id="studentList" class="student-list">
                    <!-- Students will be loaded here -->
                </div>
            </div>
        </div>

        <script>
            let students = [];

            // Update student count and list periodically
            function updateStudentData() {
                // Update recognized count
                fetch('/get_recognized_students')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            document.getElementById('studentCount').textContent =
                                `${data.recognized_students.length}/${data.total_students}`;
                        }
                    })
                    .catch(error => console.error('Error:', error));

                // Update student list
                fetch('/get_class_students')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            students = data.students;
                            renderStudentList();
                        }
                    })
                    .catch(error => console.error('Error:', error));
            }

            function renderStudentList() {
                const studentList = document.getElementById('studentList');
                if (students.length === 0) {
                    studentList.innerHTML = '<p style="text-align: center; color: #6b7280;">{get_text("no_students_enrolled", current_language)}</p>';
                    return;
                }

                studentList.innerHTML = students.map(student => {
                    const statusClass = student.status === 'Present' ? 'present' : 'absent';
                    const toggleText = student.status === 'Present' ? '{get_text("mark_absent", current_language)}' : '{get_text("mark_present", current_language)}';
                    const toggleClass = student.status === 'Present' ? 'present' : 'absent';

                    const statusText = student.status === 'Present' ? '{get_text("present", current_language)}' : '{get_text("absent", current_language)}';
                    let badges = `<span class="status-badge ${student.status.toLowerCase()}">${statusText}</span>`;
                    if (student.recognized) badges += `<span class="status-badge recognized">{get_text("recognized", current_language)}</span>`;

                    return `
                        <div class="student-item ${statusClass}">
                            <div class="student-name">${student.name}</div>
                            <div class="student-status">
                                ${badges}
                                <button class="toggle-btn ${toggleClass}" onclick="toggleStudentAttendance('${student.name}', '${student.status === 'Present' ? 'Absent' : 'Present'}')">${toggleText}</button>
                            </div>
                        </div>`;
                }).join('');
            }

            // Toggle individual student attendance
            function toggleStudentAttendance(studentName, newStatus) {
                fetch('/toggle_attendance', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        student_name: studentName,
                        status: newStatus
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update local data
                        const student = students.find(s => s.name === studentName);
                        if (student) {
                            student.status = newStatus;
                            student.manual = newStatus;
                        }
                        renderStudentList();
                    } else {
                        alert('{get_text("error_updating_attendance", current_language)}: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('{get_text("error_updating_attendance", current_language)}');
                });
            }

            function takeAttendance() {
                const btn = document.getElementById('takeAttendanceBtn');
                const saveBtn = document.getElementById('saveAttendanceBtn');
                const videoFeed = document.querySelector('.video-feed');

                btn.classList.add('loading');
                btn.textContent = '{get_text("taking", current_language)}';

                fetch('/take_attendance', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStudentData();

                        if (videoFeed) {
                            videoFeed.style.display = 'none';
                            const videoContainer = document.querySelector('.video-container');
                            if (videoContainer) {
                                const message = document.createElement('div');
                                message.style.cssText = 'padding: 60px 20px; text-align: center; background: #f8fafc; border-radius: 12px; border: 2px dashed #d1d5db; color: #6b7280; font-size: 18px;';
                                message.innerHTML = '<div style="font-size: 48px; margin-bottom: 16px;">📸</div><div>{get_text("attendance_captured_successfully", current_language)}</div><div style="font-size: 14px; margin-top: 8px; color: #9ca3af;">{get_text("video_recording_stopped", current_language)}</div>';
                                videoContainer.appendChild(message);
                            }
                        }

                        btn.style.display = 'none';
                        saveBtn.style.display = 'inline-block';
                        stopUpdates();
                        alert(`{get_text("attendance_captured", current_language).replace("{count}", "${data.recognized_count}")}`);
                    } else {
                        alert('{get_text("error_taking_attendance", current_language)}: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('{get_text("error_taking_attendance", current_language)}');
                })
                .finally(() => {
                    btn.classList.remove('loading');
                    btn.textContent = '{get_text("take_attendance", current_language)}';
                });
            }

            function saveAttendance() {
                const btn = document.getElementById('saveAttendanceBtn');
                btn.classList.add('loading');
                btn.textContent = '{get_text("saving", current_language)}';

                fetch('/save_attendance', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('{get_text("attendance_saved_successfully", current_language)}');
                        setTimeout(() => window.close(), 1000);
                    } else {
                        alert('{get_text("error_saving_attendance", current_language)}: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('{get_text("error_saving_attendance", current_language)}');
                })
                .finally(() => {
                    btn.classList.remove('loading');
                    btn.textContent = '{get_text("save_attendance", current_language)}';
                });
            }

            function stopStream() {
                fetch('/stop_stream', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    console.log('Success:', data);
                    window.close();
                })
                .catch(error => console.error('Error:', error));
            }

            let updateInterval;
            function init() {
                updateStudentData();
                updateInterval = setInterval(updateStudentData, 3000);
            }

            function stopUpdates() {
                if (updateInterval) {
                    clearInterval(updateInterval);
                    updateInterval = null;
                }
            }

            function handlePageClose() {
                fetch('/stop_stream', { method: 'POST', keepalive: true }).catch(error => console.log('Error stopping stream on page close:', error));
            }

            window.addEventListener('beforeunload', handlePageClose);
            window.addEventListener('unload', handlePageClose);
            window.addEventListener('pagehide', handlePageClose);
            document.addEventListener('DOMContentLoaded', init);
        </script>
    </body>
    </html>
    '''

    return render_template_string(
        html,
        class_id=class_id,
        class_name=class_name,
        subject_id=subject_id,
        subject_name=subject_name
    )

def run_server(host=None, port=None):
    """
    Run the video streaming server.

    Args:
        host: The host to bind the server to (default from Config)
        port: The port to bind the server to (default from Config)
    """
    from facial_recognition_system.config import Config
    if host is None:
        host = Config.HOST
    if port is None:
        port = Config.VIDEO_STREAM_PORT
    try:
        app.run(host=host, port=port, threaded=True)
    except Exception:
        raise

if __name__ == '__main__':
    run_server()
