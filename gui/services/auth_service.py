"""
Authentication service for managing user login and session management.
"""
import sqlite3
import hashlib
from datetime import datetime
from facial_recognition_system.config import Config

class AuthService:
    """Service for handling authentication and user management."""

    def __init__(self):
        self.db_path = Config.get_db_path()
        self.initialize_auth_tables()

    def initialize_auth_tables(self):
        """Initialize authentication-related database tables."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Create users table for authentication
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT UNIQUE NOT NULL,
                        password_hash TEXT NOT NULL,
                        role TEXT NOT NULL CHECK (role IN ('admin', 'teacher')),
                        is_active BOOLEAN DEFAULT 1,
                        created_at TEXT NOT NULL,
                        updated_at TEXT,
                        last_login TEXT
                    )
                ''')

                # Create teachers table for teacher-specific information
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS teachers (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER UNIQUE NOT NULL,
                        full_name TEXT NOT NULL,
                        email TEXT,
                        phone TEXT,
                        created_at TEXT NOT NULL,
                        updated_at TEXT,
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                    )
                ''')

                # Create default admin user if not exists
                self._create_default_admin(cursor)

                conn.commit()
                print("✅ Authentication tables initialized successfully")

        except Exception as e:
            print(f"❌ Failed to initialize authentication tables: {e}")

    def _create_default_admin(self, cursor):
        """Create default admin user with credentials 'admin'/'admin'."""
        cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
        admin_count = cursor.fetchone()[0]

        if admin_count == 0:
            admin_password_hash = self._hash_password("admin")
            created_at = datetime.now().isoformat()

            cursor.execute('''
                INSERT INTO users (username, password_hash, role, created_at)
                VALUES (?, ?, 'admin', ?)
            ''', ("admin", admin_password_hash, created_at))

            print("✅ Default admin user created (username: admin, password: admin)")

    def _hash_password(self, password: str) -> str:
        """Hash password using SHA-256."""
        return hashlib.sha256(password.encode()).hexdigest()

    def authenticate(self, username: str, password: str) -> dict:
        """
        Authenticate user with username and password.
        Returns user info if successful, None if failed.
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                password_hash = self._hash_password(password)

                cursor.execute('''
                    SELECT u.*, t.full_name, t.email
                    FROM users u
                    LEFT JOIN teachers t ON u.id = t.user_id
                    WHERE u.username = ? AND u.password_hash = ? AND u.is_active = 1
                ''', (username, password_hash))

                user = cursor.fetchone()

                if user:
                    # Update last login
                    cursor.execute('''
                        UPDATE users SET last_login = ? WHERE id = ?
                    ''', (datetime.now().isoformat(), user['id']))
                    conn.commit()

                    return {
                        'id': user['id'],
                        'username': user['username'],
                        'role': user['role'],
                        'full_name': user['full_name'] or user['username'],
                        'email': user['email'],
                        'last_login': user['last_login']
                    }

                return None

        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return None

    def create_teacher(self, username: str, password: str, full_name: str, email: str = None, phone: str = None) -> bool:
        """Create a new teacher account."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Check if username already exists
                cursor.execute("SELECT COUNT(*) FROM users WHERE username = ?", (username,))
                if cursor.fetchone()[0] > 0:
                    return False

                password_hash = self._hash_password(password)
                created_at = datetime.now().isoformat()

                # Create user account
                cursor.execute('''
                    INSERT INTO users (username, password_hash, role, created_at)
                    VALUES (?, ?, 'teacher', ?)
                ''', (username, password_hash, created_at))

                user_id = cursor.lastrowid

                # Create teacher profile
                cursor.execute('''
                    INSERT INTO teachers (user_id, full_name, email, phone, created_at)
                    VALUES (?, ?, ?, ?, ?)
                ''', (user_id, full_name, email, phone, created_at))

                conn.commit()
                return True

        except Exception as e:
            print(f"❌ Failed to create teacher: {e}")
            return False

    def get_all_teachers(self) -> list:
        """Get all teacher accounts with their information."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT u.id, u.username, u.is_active, u.created_at, u.last_login,
                           t.full_name, t.email, t.phone
                    FROM users u
                    JOIN teachers t ON u.id = t.user_id
                    WHERE u.role = 'teacher'
                    ORDER BY t.full_name
                ''')

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            print(f"❌ Failed to get teachers: {e}")
            return []

    def update_teacher_status(self, user_id: int, is_active: bool) -> bool:
        """Update teacher active status."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE users SET is_active = ?, updated_at = ?
                    WHERE id = ? AND role = 'teacher'
                ''', (is_active, datetime.now().isoformat(), user_id))

                conn.commit()
                return cursor.rowcount > 0

        except Exception as e:
            print(f"❌ Failed to update teacher status: {e}")
            return False

    def delete_teacher(self, user_id: int) -> bool:
        """Delete a teacher account."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    DELETE FROM users WHERE id = ? AND role = 'teacher'
                ''', (user_id,))

                conn.commit()
                return cursor.rowcount > 0

        except Exception as e:
            print(f"❌ Failed to delete teacher: {e}")
            return False

    def get_admin_statistics(self) -> dict:
        """Get comprehensive statistics for admin dashboard."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                stats = {}

                # Teacher count
                cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'teacher'")
                stats['total_teachers'] = cursor.fetchone()[0]

                # Class count
                cursor.execute("SELECT COUNT(*) FROM classes")
                stats['total_classes'] = cursor.fetchone()[0]

                # Student count
                cursor.execute("SELECT COUNT(*) FROM etudiants")
                stats['total_students'] = cursor.fetchone()[0]

                # Subject count
                cursor.execute("SELECT COUNT(*) FROM matieres")
                stats['total_subjects'] = cursor.fetchone()[0]

                # Quiz count
                cursor.execute("SELECT COUNT(*) FROM quiz")
                stats['total_quizzes'] = cursor.fetchone()[0]

                # Recent attendance count (last 7 days)
                cursor.execute('''
                    SELECT COUNT(*) FROM presences
                    WHERE date >= date('now', '-7 days')
                ''')
                stats['recent_attendance'] = cursor.fetchone()[0]

                return stats

        except Exception as e:
            print(f"❌ Failed to get admin statistics: {e}")
            return {}
