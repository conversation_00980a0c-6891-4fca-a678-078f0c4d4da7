"""
Class management service for the Teacher Assistant application.
"""
from facial_recognition_system.local_database import (
    get_existing_classes as local_get_existing_classes,
    create_class as local_create_class,
    update_class_name as local_update_class_name,
    delete_class as local_delete_class,
    get_students_in_class as local_get_students_in_class,
    assign_student_to_class as local_assign_student_to_class,
    remove_student_from_class as local_remove_student_from_class
)


def get_existing_classes(teacher_id=None):
    """Fetch all existing classes from local database, optionally filtered by teacher"""
    return local_get_existing_classes(teacher_id)


def create_class(class_name, description="", teacher_id=None):
    """Create a new class in the local database"""
    return local_create_class(class_name, description, teacher_id)


def update_class_name(class_id, new_name):
    """Update the name of a class in the local database"""
    return local_update_class_name(class_id, new_name)


def delete_class(class_id):
    """Delete a class from the local database"""
    return local_delete_class(class_id)


def get_students_in_class(class_id):
    """Get all students in a specific class"""
    return local_get_students_in_class(class_id)


def assign_student_to_class(student_id, class_id):
    """Assign a student to a class"""
    return local_assign_student_to_class(student_id, class_id)


def remove_student_from_class(student_id):
    """Remove a student from their class"""
    return local_remove_student_from_class(student_id)
