"""
Subject management service for the Teacher Assistant application.
"""
from datetime import datetime
from facial_recognition_system.local_database import get_connection


def get_class_subjects(class_id=None, teacher_id=None):
    """Fetch class subjects from local database

    Args:
        class_id (str, optional): ID of the class to fetch subjects for. If None, fetch all subjects.
        teacher_id (str, optional): ID of the teacher to filter subjects for.

    Returns:
        list: List of subject dictionaries
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        query = "SELECT * FROM matieres"
        params = []
        conditions = []

        if class_id:
            conditions.append("class_id = ?")
            params.append(class_id)
        if teacher_id:
            conditions.append("teacher_id = ?")
            params.append(teacher_id)

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        query += " ORDER BY name"

        cursor.execute(query, params)
        rows = cursor.fetchall()

        if not rows:
            return []

        # Convert rows to list of dictionaries
        subjects = []
        for row in rows:
            subject = dict(row)
            # Rename 'name' to 'subject_name' for compatibility
            subject['subject_name'] = subject.pop('name')
            # Add empty teacher_name for compatibility
            subject['teacher_name'] = ""
            subjects.append(subject)

        return subjects
    except Exception as e:
        return []


def create_class_subject(class_id, subject_name, description="", teacher_name="", teacher_id=None):
    """Create a new class subject in the local database

    Args:
        class_id (str): ID of the class
        subject_name (str): Name of the subject (e.g., Math, Computer Science)
        description (str, optional): Description of the subject
        teacher_name (str, optional): Name of the teacher (not used in local database)
        teacher_id (str, optional): ID of the teacher creating the subject

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if subject already exists for this class and teacher
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute(
            "SELECT * FROM matieres WHERE class_id = ? AND name = ? AND teacher_id = ?",
            (class_id, subject_name, teacher_id)
        )
        existing = cursor.fetchone()

        if existing:
            return False

        # Create new subject
        now = datetime.now().isoformat()

        cursor.execute(
            "INSERT INTO matieres (name, class_id, description, teacher_id, created_at) VALUES (?, ?, ?, ?, ?)",
            (subject_name, class_id, description, teacher_id, now)
        )
        conn.commit()

        return True
    except Exception as e:
        return False


def update_class_subject(subject_id, subject_name=None, description=None, teacher_name=None):
    """Update a class subject in the local database

    Args:
        subject_id (str): ID of the subject to update
        subject_name (str, optional): New name for the subject
        description (str, optional): New description for the subject
        teacher_name (str, optional): New teacher name (not used in local database)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if subject exists
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM matieres WHERE id = ?", (subject_id,))
        existing_subject = cursor.fetchone()

        if not existing_subject:
            return False

        # Update subject
        now = datetime.now().isoformat()

        # Build the SQL query dynamically based on what needs to be updated
        update_fields = ["updated_at = ?"]
        params = [now]

        if subject_name is not None:
            update_fields.append("name = ?")
            params.append(subject_name)

        if description is not None:
            update_fields.append("description = ?")
            params.append(description)

        # Add the subject_id to the parameters
        params.append(subject_id)

        # Construct and execute the SQL query
        sql = f"UPDATE matieres SET {', '.join(update_fields)} WHERE id = ?"
        cursor.execute(sql, params)
        conn.commit()

        return True
    except Exception as e:
        return False


def delete_class_subject(subject_id):
    """Delete a class subject from the local database

    Args:
        subject_id (str): ID of the subject to delete

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if subject exists
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM matieres WHERE id = ?", (subject_id,))
        existing_subject = cursor.fetchone()

        if not existing_subject:
            return False

        # Delete subject
        cursor.execute("DELETE FROM matieres WHERE id = ?", (subject_id,))
        conn.commit()

        return True
    except Exception as e:
        return False
