
"""
Student management service for the Teacher Assistant application.
"""
from gui.services.class_service import get_existing_classes, get_students_in_class

# Cache for student class information
_student_class_cache = {}

def get_student_class(student_id):
    # Check if we have this student's class in the cache
    if student_id in _student_class_cache:
        return _student_class_cache[student_id]

    # If not in cache, we need to query the database
    # This is more efficient than the previous implementation
    # as it only queries each class once
    classes = get_existing_classes()

    # Build a cache of all students' classes
    for class_name, class_info in classes.items():
        class_id = class_info['id']
        students = get_students_in_class(class_id)

        # Add all students from this class to the cache
        for _, sid in students:
            _student_class_cache[sid] = (class_id, class_name)

    # Return from cache (or None, None if not found)
    return _student_class_cache.get(student_id, (None, None))
