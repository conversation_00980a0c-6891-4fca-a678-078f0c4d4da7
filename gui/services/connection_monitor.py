"""
Connection Monitor Service

This service monitors for incoming connections and triggers the switch from QR to face display.
"""

import threading
from typing import Optional, Callable


# Monitor using Flet's page connections
class FletConnectionMonitor:
    """Monitor Flet page connections."""

    def __init__(self):
        self.monitoring = False
        self.connection_callback: Optional[Callable] = None
        self.connection_detected = False
        self.connected_sessions = set()

    def on_page_connect(self, page):
        """Called when a page connects."""
        session_id = getattr(page, 'session_id', id(page))

        if session_id not in self.connected_sessions:
            self.connected_sessions.add(session_id)
            print(f"📱 New page connected (session: {session_id})")

            if not self.connection_detected:
                self.connection_detected = True
                print("✅ First connection detected! Switching to face display...")

                if self.connection_callback:
                    # Run callback in a separate thread to avoid blocking
                    callback_thread = threading.Thread(
                        target=self.connection_callback,
                        daemon=True
                    )
                    callback_thread.start()

    def start_monitoring(self, callback: Callable):
        """Start monitoring for page connections."""
        self.connection_callback = callback
        self.monitoring = True
        self.connection_detected = False
        self.connected_sessions.clear()

    def stop_monitoring(self):
        """Stop monitoring."""
        self.monitoring = False
        self.connected_sessions.clear()


# Global instance
_flet_monitor = None

def get_flet_monitor() -> FletConnectionMonitor:
    """Get the global Flet connection monitor instance."""
    global _flet_monitor
    if _flet_monitor is None:
        _flet_monitor = FletConnectionMonitor()
    return _flet_monitor

def start_flet_monitoring(callback: Callable):
    """Start monitoring for Flet page connections."""
    monitor = get_flet_monitor()
    monitor.start_monitoring(callback)

def on_page_connected(page):
    """Handle page connection event."""
    monitor = get_flet_monitor()
    if monitor.monitoring:
        monitor.on_page_connect(page)
