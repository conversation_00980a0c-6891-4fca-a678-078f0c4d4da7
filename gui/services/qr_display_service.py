"""
QR Code Display Service

This service displays a QR code on the screen until a connection is made.
"""

import os
import pygame
import qrcode
import threading
import math
import random
from typing import Optional
from PIL import Image
from gui.config.language import get_text, DEFAULT_LANGUAGE


class QRDisplayService:
    """Service for displaying QR codes on screen."""

    def __init__(self, language=DEFAULT_LANGUAGE):
        self.language = language
        self.running = False
        self.thread: Optional[threading.Thread] = None
        self.screen = None
        self.clock = None
        self.qr_url = ""
        self.display_mode = "qr"  # "qr", "face", "enrollment", or "quiz"
        self.enrollment_qr_url = ""
        self.enrollment_class_name = ""
        self.quiz_qr_url = ""
        self.quiz_title = ""

        # Colors
        self.WHITE = (255, 255, 255)
        self.BLACK = (0, 0, 0)
        self.BLUE = (0, 100, 200)
        self.MY_COLOR = (200,200,255)
        self.BUTTON_COLOR = (70, 130, 180)
        self.BUTTON_HOVER_COLOR = (100, 149, 237)
        self.BUTTON_TEXT_COLOR = (255, 255, 255)

        # Screen dimensions
        self.WIDTH = 0
        self.HEIGHT = 0

        # Mouse interaction state
        self.mouse_down_time = 0
        self.mouse_down_pos = None
        self.long_press_threshold = 1000  # 1 second in milliseconds
        self.show_buttons = False
        self.button_fade_start = 0
        self.button_fade_duration = 3000  # 3 seconds

        # Button definitions
        self.buttons = []
        self.hovered_button = None

        # Message display
        self.message_text = ""
        self.message_start_time = 0
        self.message_duration = 0

        # WiFi interface state
        self.wifi_networks = []
        self.selected_network_index = 0
        self.wifi_password = ""
        self.wifi_input_mode = "selection"  # "selection", "password", "keyboard"
        self.show_password = False  # Toggle for password visibility

        # On-screen keyboard state
        self.keyboard_layout = self._create_keyboard_layout()
        self.keyboard_selected_row = 0
        self.keyboard_selected_col = 0
        self.keyboard_shift = False
        self.keyboard_caps = False

        # Touch feedback
        self.touch_feedback_pos = None
        self.touch_feedback_time = 0
        self.touch_feedback_duration = 200  # 200ms touch feedback

        # Face display variables (copied from face_display_service.py)
        self.LOOK_LEFT = -65
        self.LOOK_CENTER = -15
        self.LOOK_RIGHT = 35

        # Animation state variables
        self.eye_visible = True
        self.last_blink = 0
        self.blink_duration = 150
        self.blink_pause = 200
        self.next_blink_time = random.randint(2000, 9000)
        self.will_double_blink = False
        self.in_double_blink = False

        # Eye movement variables
        self.iris_position_left = self.LOOK_CENTER
        self.iris_position_right = self.LOOK_CENTER
        self.target_position_left = self.LOOK_CENTER
        self.target_position_right = self.LOOK_CENTER
        self.movement_speed = 2

        # Movement timing
        self.last_movement = 0
        self.movement_interval = random.randint(2000, 5000)
        self.is_moving = False

    def setup_display_environment(self):
        """Setup environment variables to avoid GLX/OpenGL issues."""
        # Check if we're in a headless environment
        is_headless = 'SSH_CLIENT' in os.environ or 'SSH_TTY' in os.environ or os.environ.get('DISPLAY', '') == ''

        if is_headless:
            print("🔍 Detected headless/SSH environment, using software rendering")
            # For headless environments, use dummy video driver
            os.environ['SDL_VIDEODRIVER'] = 'dummy'
        else:
            # For environments with display, use X11 with software rendering
            os.environ['SDL_VIDEODRIVER'] = 'x11'
            os.environ['SDL_VIDEO_X11_FORCE_EGL'] = '0'
            os.environ['SDL_VIDEO_X11_NODIRECTCOLOR'] = '1'

        # Disable hardware acceleration that can cause GLX issues
        os.environ['SDL_RENDER_DRIVER'] = 'software'
        os.environ['SDL_FRAMEBUFFER_ACCELERATION'] = '0'

        # Additional fallback options
        if 'DISPLAY' not in os.environ:
            os.environ['DISPLAY'] = ':0'

        print(f"🔧 Display environment: SDL_VIDEODRIVER={os.environ.get('SDL_VIDEODRIVER')}")

    def initialize_pygame(self) -> bool:
        """Initialize pygame and create the display."""
        try:
            # Setup environment to avoid GLX errors
            self.setup_display_environment()

            # Initialize pygame with error handling
            try:
                pygame.init()
                print("✅ Pygame initialized successfully")
            except Exception as e:
                print(f"⚠️ Pygame init warning: {e}")
                # Try to continue anyway

            # Try different display modes with fallbacks - prioritize windowed for 1024x600
            display_modes = [
                ("fullscreen", lambda: self._try_fullscreen_mode()),
                ("windowed", lambda: self._try_windowed_mode()),
                ("minimal", lambda: self._try_minimal_mode())
            ]

            for mode_name, mode_func in display_modes:
                try:
                    if mode_func():
                        print(f"✅ {get_text('qr_display_initialized', self.language).format(mode=mode_name, width=self.WIDTH, height=self.HEIGHT)}")
                        break
                except Exception as e:
                    print(f"⚠️ {get_text('mode_failed', self.language).format(mode=mode_name.capitalize())}: {e}")
                    continue
            else:
                print(f"❌ {get_text('all_display_modes_failed', self.language)}")
                return False

            pygame.display.set_caption(get_text("teacher_assistant_qr_code", self.language))
            self.clock = pygame.time.Clock()

            # Show mouse cursor for interaction
            try:
                pygame.mouse.set_visible(True)
            except Exception as e:
                print(f"⚠️ {get_text('could_not_set_mouse_cursor', self.language)}: {e}")

            return True
        except Exception as e:
            print(f"❌ {get_text('failed_to_initialize_pygame', self.language)}: {e}")
            return False

    def _try_windowed_mode(self) -> bool:
        """Try to initialize pygame in windowed mode optimized for 1024x600."""
        self.WIDTH, self.HEIGHT = 1024, 600
        self.screen = pygame.display.set_mode((self.WIDTH, self.HEIGHT))
        return True

    def _try_fullscreen_mode(self) -> bool:
        """Try to initialize pygame in fullscreen mode."""
        screen_info = pygame.display.Info()
        self.WIDTH, self.HEIGHT = screen_info.current_w, screen_info.current_h
        self.screen = pygame.display.set_mode((self.WIDTH, self.HEIGHT), pygame.FULLSCREEN)
        return True

    def _try_minimal_mode(self) -> bool:
        """Try to initialize pygame in minimal mode (smallest possible window)."""
        self.WIDTH, self.HEIGHT = 800, 600
        self.screen = pygame.display.set_mode((self.WIDTH, self.HEIGHT), pygame.NOFRAME)
        return True

    def generate_qr_surface(self, url: str):
        """Generate a pygame surface with the QR code."""
        try:
            # Generate QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(url)
            qr.make(fit=True)

            # Create QR code image
            qr_img = qr.make_image(fill_color="black", back_color="white")

            # Convert PIL image to pygame surface - optimized for 1024x600
            qr_size = min(self.WIDTH // 3, self.HEIGHT // 2)  # Smaller QR code for compact screen
            qr_img = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)

            # Convert to RGB mode for pygame
            qr_img = qr_img.convert('RGB')

            # Convert to pygame surface
            mode = qr_img.mode
            size = qr_img.size
            data = qr_img.tobytes()

            qr_surface = pygame.image.fromstring(data, size, mode)
            return qr_surface

        except Exception as e:
            print(f"❌ {get_text('failed_to_generate_qr_code', self.language)}: {e}")
            return None

    def draw_qr_screen(self):
        """Draw the QR code screen with instructions."""
        # Fill background
        self.screen.fill(self.WHITE)

        # Generate QR code surface
        qr_surface = self.generate_qr_surface(self.qr_url)

        if qr_surface:
            # Center the QR code - optimized for 1024x600
            qr_rect = qr_surface.get_rect()
            qr_rect.center = (self.WIDTH // 2, self.HEIGHT // 2 - 20)  # Less vertical offset

            # Draw border around QR code
            border_padding = 8
            border_rect = pygame.Rect(
                qr_rect.x - border_padding,
                qr_rect.y - border_padding,
                qr_rect.width + 2 * border_padding,
                qr_rect.height + 2 * border_padding
            )
            pygame.draw.rect(self.screen, self.BLACK, border_rect, 2)  # 2px black border with raduis

            # Draw white background inside border
            inner_rect = pygame.Rect(
                qr_rect.x - border_padding + 2,
                qr_rect.y - border_padding + 2,
                qr_rect.width + 2 * (border_padding - 2),
                qr_rect.height + 2 * (border_padding - 2)
            )
            pygame.draw.rect(self.screen, self.WHITE, inner_rect)

            self.screen.blit(qr_surface, qr_rect)

        # Draw title text - optimized for 1024x600
        try:
            font_small = pygame.font.Font(None, 32)  # Slightly smaller font

            # Display IP:Port combination
            ip_port_text = self.qr_url.replace("http://", "")
            ip_port_display = font_small.render(ip_port_text, True, self.BLUE)
            ip_port_rect = ip_port_display.get_rect()
            ip_port_rect.centerx = self.WIDTH // 2
            ip_port_rect.y = self.HEIGHT - 30  # Closer to bottom for compact screen
            self.screen.blit(ip_port_display, ip_port_rect)

        except Exception as e:
            print(f"⚠️ {get_text('font_rendering_failed', self.language)}: {e}")

    def draw_mouth(self):
        """Draw the mouth on the screen - optimized for 1024x600."""
        # Smaller mouth for compact screen
        mouth_width = min(120, self.WIDTH // 8)  # Adaptive mouth size
        mouth_height = 30
        mouth_center_x = self.WIDTH // 2
        mouth_center_y = self.HEIGHT // 2 + min(80, self.HEIGHT // 8)  # Adaptive positioning

        mouth_rect = pygame.Rect(
            mouth_center_x - mouth_width//2,
            mouth_center_y - mouth_height//2,
            mouth_width,
            mouth_height
        )
        pygame.draw.arc(self.screen, self.WHITE, mouth_rect, math.pi, 2*math.pi, 10)

    def draw_eyes(self, eye_visible: bool = True):
        """Draw the eyes on the screen - optimized for 1024x600."""
        # Smaller eyes for compact screen
        eye_radius = min(80, self.HEIGHT // 8)  # Adaptive eye size
        eye_outline_width = 5
        eye_spacing = min(120, self.WIDTH // 8)  # Adaptive spacing
        left_eye_center = ((self.WIDTH//2)-eye_spacing, self.HEIGHT//2)
        right_eye_center = ((self.WIDTH//2)+eye_spacing, self.HEIGHT//2)

        if not eye_visible:
            # Draw black rectangles when eyes are closed
            rect_width = eye_radius * 2
            rect_height = eye_radius // 2
            for eye_center in [left_eye_center, right_eye_center]:
                pygame.draw.rect(self.screen, self.BLACK, (
                    eye_center[0] - rect_width//2,
                    eye_center[1] - rect_height//2,
                    rect_width,
                    rect_height
                ))
            return

        # Draw base white eyes and black outlines
        for eye_center in [left_eye_center, right_eye_center]:
            pygame.draw.circle(self.screen, self.WHITE, eye_center, eye_radius)
            pygame.draw.circle(self.screen, self.BLACK, eye_center, eye_radius, eye_outline_width)

        # Draw irises and pupils - adaptive sizing
        iris_width = min(50, eye_radius // 2)  # Proportional to eye size
        iris_height = min(60, eye_radius * 3 // 4)

        # Left eye
        x_offset = 30 + self.iris_position_left
        iris_rect = pygame.Rect(
            left_eye_center[0] - iris_width/2 + x_offset,
            left_eye_center[1] - iris_height/2,
            iris_width,
            iris_height
        )
        pygame.draw.ellipse(self.screen, self.BLACK, iris_rect)
        pupil_center = (left_eye_center[0] + x_offset, left_eye_center[1])
        pupil_size = max(8, eye_radius // 8)  # Adaptive pupil size
        pygame.draw.circle(self.screen, self.WHITE, pupil_center, pupil_size)

        # Right eye
        x_offset = -(30 + self.iris_position_right)
        iris_rect = pygame.Rect(
            right_eye_center[0] - iris_width/2 + x_offset,
            right_eye_center[1] - iris_height/2,
            iris_width,
            iris_height
        )
        pygame.draw.ellipse(self.screen, self.BLACK, iris_rect)
        pupil_center = (right_eye_center[0] + x_offset, right_eye_center[1])
        pygame.draw.circle(self.screen, self.WHITE, pupil_center, pupil_size)

    def set_random_eye_positions(self):
        """Set random eye positions."""
        x = random.random()
        if x < 0.6:
            return self.LOOK_CENTER, self.LOOK_CENTER
        else:
            positions = [self.LOOK_LEFT, self.LOOK_RIGHT]
            pos1 = random.choice(positions)
            pos2 = self.LOOK_RIGHT if pos1 == self.LOOK_LEFT else self.LOOK_LEFT
            return pos1, pos2

    def trigger_single_blink(self):
        """Trigger a single blink animation."""
        self.eye_visible = False
        self.screen.fill(self.MY_COLOR)
        self.draw_eyes(self.eye_visible)
        self.draw_mouth()
        pygame.display.flip()
        pygame.time.wait(100)
        self.eye_visible = True
        self.screen.fill(self.MY_COLOR)
        self.draw_eyes(self.eye_visible)
        self.draw_mouth()
        pygame.display.flip()
        self.last_blink = pygame.time.get_ticks()

    def update_face_animation(self):
        """Update the face animation state."""
        current_time = pygame.time.get_ticks()

        # Handle automatic movement
        if current_time - self.last_movement > self.movement_interval:
            self.trigger_single_blink()
            pygame.time.wait(50)

            new_left, new_right = self.set_random_eye_positions()
            self.target_position_left = new_left
            self.target_position_right = new_right

            self.last_movement = current_time
            self.movement_interval = random.randint(2000, 3000)

        # Smooth iris movement
        self.is_moving = False
        if (self.iris_position_left != self.target_position_left or
            self.iris_position_right != self.target_position_right):
            self.is_moving = True

        if self.iris_position_left < self.target_position_left:
            self.iris_position_left = min(self.iris_position_left + self.movement_speed,
                                        self.target_position_left)
        elif self.iris_position_left > self.target_position_left:
            self.iris_position_left = max(self.iris_position_left - self.movement_speed,
                                        self.target_position_left)

        if self.iris_position_right < self.target_position_right:
            self.iris_position_right = min(self.iris_position_right + self.movement_speed,
                                         self.target_position_right)
        elif self.iris_position_right > self.target_position_right:
            self.iris_position_right = max(self.iris_position_right - self.movement_speed,
                                         self.target_position_right)

        # Handle blinking
        if not self.is_moving:
            if self.eye_visible and current_time - self.last_blink > self.next_blink_time:
                self.eye_visible = False
                self.last_blink = current_time
                self.will_double_blink = random.choice([True, False])
            elif not self.eye_visible and current_time - self.last_blink > self.blink_duration:
                self.eye_visible = True
                self.last_blink = current_time
                if self.will_double_blink and not self.in_double_blink:
                    self.next_blink_time = self.blink_pause
                    self.in_double_blink = True
                else:
                    self.next_blink_time = random.randint(1500, 3000)
                    self.in_double_blink = False

    def draw_face_screen(self):
        """Draw the face display screen."""
        # Fill background
        self.screen.fill(self.MY_COLOR)

        # Update animation
        self.update_face_animation()

        # Draw face
        self.draw_eyes(self.eye_visible)
        self.draw_mouth()

    def draw_enrollment_qr_screen(self):
        """Draw the enrollment QR code screen."""
        # Fill background
        self.screen.fill(self.BLACK)

        # Generate QR code surface for enrollment
        qr_surface = self.generate_qr_surface(self.enrollment_qr_url)

        if qr_surface:
            # Center the QR code
            qr_rect = qr_surface.get_rect()
            qr_rect.center = (self.WIDTH // 2, self.HEIGHT // 2 - 40)

            # Draw border around QR code
            border_padding = 8
            border_rect = pygame.Rect(
                qr_rect.x - border_padding,
                qr_rect.y - border_padding,
                qr_rect.width + 2 * border_padding,
                qr_rect.height + 2 * border_padding
            )
            pygame.draw.rect(self.screen, self.WHITE, border_rect, 2)

            # Draw white background inside border
            inner_rect = pygame.Rect(
                qr_rect.x - border_padding + 2,
                qr_rect.y - border_padding + 2,
                qr_rect.width + 2 * (border_padding - 2),
                qr_rect.height + 2 * (border_padding - 2)
            )
            pygame.draw.rect(self.screen, self.WHITE, inner_rect)

            # Draw QR code
            self.screen.blit(qr_surface, qr_rect)

            # Draw title
            font_large = pygame.font.Font(None, 48)
            title_text = font_large.render(get_text("enrollment_title", self.language).format(class_name=self.enrollment_class_name), True, self.WHITE)
            title_rect = title_text.get_rect(center=(self.WIDTH // 2, qr_rect.bottom + 40))
            self.screen.blit(title_text, title_rect)

            # Draw instruction
            font_small = pygame.font.Font(None, 32)
            instruction_text = font_small.render(get_text("students_can_scan_enrollment", self.language), True, self.WHITE)
            instruction_rect = instruction_text.get_rect(center=(self.WIDTH // 2, title_rect.bottom + 30))
            self.screen.blit(instruction_text, instruction_rect)

    def draw_quiz_qr_screen(self):
        """Draw the quiz QR code screen."""
        # Fill background
        self.screen.fill(self.BLACK)

        # Generate QR code surface for quiz
        qr_surface = self.generate_qr_surface(self.quiz_qr_url)

        if qr_surface:
            # Center the QR code
            qr_rect = qr_surface.get_rect()
            qr_rect.center = (self.WIDTH // 2, self.HEIGHT // 2 - 40)

            # Draw border around QR code
            border_padding = 8
            border_rect = pygame.Rect(
                qr_rect.x - border_padding,
                qr_rect.y - border_padding,
                qr_rect.width + 2 * border_padding,
                qr_rect.height + 2 * border_padding
            )
            pygame.draw.rect(self.screen, self.WHITE, border_rect, 2)

            # Draw white background inside border
            inner_rect = pygame.Rect(
                qr_rect.x - border_padding + 2,
                qr_rect.y - border_padding + 2,
                qr_rect.width + 2 * (border_padding - 2),
                qr_rect.height + 2 * (border_padding - 2)
            )
            pygame.draw.rect(self.screen, self.WHITE, inner_rect)

            # Draw QR code
            self.screen.blit(qr_surface, qr_rect)

            # Draw title
            font_large = pygame.font.Font(None, 48)
            title_text = font_large.render(get_text("quiz_title_display", self.language).format(quiz_title=self.quiz_title), True, self.WHITE)
            title_rect = title_text.get_rect(center=(self.WIDTH // 2, qr_rect.bottom + 40))
            self.screen.blit(title_text, title_rect)

            # Draw instruction
            font_small = pygame.font.Font(None, 32)
            instruction_text = font_small.render(get_text("students_can_scan_quiz", self.language), True, self.WHITE)
            instruction_rect = instruction_text.get_rect(center=(self.WIDTH // 2, title_rect.bottom + 30))
            self.screen.blit(instruction_text, instruction_rect)

    def switch_to_face_mode(self):
        """Switch to face display mode."""
        print(f"🔄 {get_text('switching_to_face_mode', self.language)}")
        self.display_mode = "face"

        # Initialize face animation timing
        self.last_blink = pygame.time.get_ticks()
        self.last_movement = pygame.time.get_ticks()

        # Initialize buttons for face mode
        self.setup_buttons()

    def switch_to_enrollment_mode(self, enrollment_url: str, class_name: str):
        """Switch to enrollment QR code display mode."""
        print(f"🔄 {get_text('switching_to_enrollment_qr', self.language).format(class_name=class_name)}")
        self.display_mode = "enrollment"
        self.enrollment_qr_url = enrollment_url
        self.enrollment_class_name = class_name
        self.show_buttons = False

    def switch_back_to_face_from_enrollment(self):
        """Switch back to face display from enrollment mode."""
        print(f"🔄 {get_text('switching_back_to_face_from_enrollment', self.language)}")
        self.display_mode = "face"
        self.enrollment_qr_url = ""
        self.enrollment_class_name = ""

        # Initialize face animation timing
        self.last_blink = pygame.time.get_ticks()
        self.last_movement = pygame.time.get_ticks()

        # Initialize buttons for face mode
        self.setup_buttons()

    def switch_to_quiz_mode(self, quiz_url: str, quiz_title: str):
        """Switch to quiz QR code display mode."""
        print(f"🔄 {get_text('switching_to_quiz_qr', self.language).format(quiz_title=quiz_title)}")
        self.display_mode = "quiz"
        self.quiz_qr_url = quiz_url
        self.quiz_title = quiz_title
        self.show_buttons = False

    def switch_back_to_face_from_quiz(self):
        """Switch back to face display from quiz mode."""
        print(f"🔄 {get_text('switching_back_to_face_from_quiz', self.language)}")
        self.display_mode = "face"
        self.quiz_qr_url = ""
        self.quiz_title = ""

        # Initialize face animation timing
        self.last_blink = pygame.time.get_ticks()
        self.last_movement = pygame.time.get_ticks()

        # Initialize buttons for face mode
        self.setup_buttons()

    def setup_buttons(self):
        """Setup button positions and properties for touch interface - optimized for 1024x600."""
        if self.WIDTH == 0 or self.HEIGHT == 0:
            return

        # Adjust button size for smaller screen
        button_width = min(250, self.WIDTH // 4)  # Adaptive button width
        button_height = min(60, self.HEIGHT // 10)  # Adaptive button height
        button_spacing = 15  # Reduced spacing

        # Center buttons horizontally
        start_x = (self.WIDTH - button_width) // 2
        start_y = self.HEIGHT - 140  # Less space from bottom for compact screen

        # Different buttons based on display mode
        if self.display_mode == "qr":
            self.buttons = [
                {
                    'rect': pygame.Rect(start_x, start_y, button_width, button_height),
                    'text': get_text('show_face', self.language),
                    'action': 'show_face'
                },
                {
                    'rect': pygame.Rect(start_x, start_y + button_height + button_spacing, button_width, button_height),
                    'text': get_text('wifi_settings', self.language),
                    'action': 'wifi_settings'
                }
            ]
        elif self.display_mode == "enrollment":
            self.buttons = [
                {
                    'rect': pygame.Rect(start_x, start_y, button_width, button_height),
                    'text': get_text('close_enrollment', self.language),
                    'action': 'close_enrollment'
                },
                {
                    'rect': pygame.Rect(start_x, start_y + button_height + button_spacing, button_width, button_height),
                    'text': get_text('wifi_settings', self.language),
                    'action': 'wifi_settings'
                }
            ]
        elif self.display_mode == "quiz":
            self.buttons = [
                {
                    'rect': pygame.Rect(start_x, start_y, button_width, button_height),
                    'text': get_text('close_quiz', self.language),
                    'action': 'close_quiz'
                },
                {
                    'rect': pygame.Rect(start_x, start_y + button_height + button_spacing, button_width, button_height),
                    'text': get_text('wifi_settings', self.language),
                    'action': 'wifi_settings'
                }
            ]
        else:  # face mode
            self.buttons = [
                {
                    'rect': pygame.Rect(start_x, start_y, button_width, button_height),
                    'text': get_text('show_qr_code', self.language),
                    'action': 'qr_code'
                },
                {
                    'rect': pygame.Rect(start_x, start_y + button_height + button_spacing, button_width, button_height),
                    'text': get_text('wifi_settings', self.language),
                    'action': 'wifi_settings'
                }
            ]

    def handle_button_click(self, action: str):
        """Handle button click actions."""
        if action == 'qr_code':
            self.show_qr_code_action()
        elif action == 'show_face':
            self.show_face_action()
        elif action == 'close_enrollment':
            self.close_enrollment_action()
        elif action == 'close_quiz':
            self.close_quiz_action()
        elif action == 'wifi_settings':
            self.show_wifi_settings_action()

    def show_qr_code_action(self):
        """Switch back to QR code display."""
        print("🔄 Switching back to QR code display...")
        # Update QR URL with current IP
        from gui.utils.network import get_local_ip
        import os
        port = os.environ.get('FLET_PORT', '8550')
        ip_address = get_local_ip()
        self.qr_url = f"http://{ip_address}:{port}"

        self.display_mode = "qr"
        self.show_buttons = False

    def show_face_action(self):
        """Switch to face display mode."""
        print("🔄 Switching to face display...")
        self.display_mode = "face"
        self.show_buttons = False

        # Initialize face animation timing
        self.last_blink = pygame.time.get_ticks()
        self.last_movement = pygame.time.get_ticks()

    def close_enrollment_action(self):
        """Close enrollment and return to face display."""
        print("🔄 Closing enrollment and returning to face display...")
        # Stop enrollment server
        from gui.utils.qr_code import stop_enrollment_server
        stop_enrollment_server()
        # Switch back to face display
        self.switch_back_to_face_from_enrollment()

    def close_quiz_action(self):
        """Close quiz and return to face display."""
        print("🔄 Closing quiz and returning to face display...")
        # Stop quiz server
        from gui.utils.qr_code import stop_quiz_server
        stop_quiz_server()
        # Switch back to face display
        self.switch_back_to_face_from_quiz()

    def show_wifi_settings_action(self):
        """Show WiFi settings interface."""
        print("📶 Opening WiFi settings...")
        self.show_wifi_interface()

    def show_wifi_interface(self):
        """Display WiFi selection interface."""
        from gui.utils.wifi_manager import get_wifi_manager

        # Scan for networks
        wifi_manager = get_wifi_manager()
        print("🔍 Scanning for WiFi networks...")
        networks = wifi_manager.scan_networks()

        if not networks:
            self.show_message("No WiFi networks found", 3000)
            return

        # Debug: Print found networks
        print(f"📡 Found {len(networks)} networks:")
        for i, network in enumerate(networks):
            print(f"  {i+1}. {network.ssid} - {network.signal_strength}% - {'Secured' if network.is_secured else 'Open'}")

        # Show WiFi selection screen
        self.display_mode = "wifi"
        self.wifi_networks = networks
        self.selected_network_index = 0
        self.wifi_password = ""
        self.wifi_input_mode = "selection"  # "selection" or "password"

    def draw_wifi_interface(self):
        """Draw the WiFi selection interface - optimized for 1024x600."""
        self.screen.fill(self.BLACK)

        # Title - smaller for compact screen
        font_large = pygame.font.Font(None, 36)  # Reduced font size
        title_text = font_large.render("WiFi Networks", True, self.WHITE)
        title_rect = title_text.get_rect(center=(self.WIDTH // 2, 30))  # Higher position
        self.screen.blit(title_text, title_rect)

        # Cancel button in top right
        self.draw_cancel_button()

        if self.wifi_input_mode == "selection":
            self.draw_network_list()
        elif self.wifi_input_mode == "password":
            self.draw_password_input()
        elif self.wifi_input_mode == "keyboard":
            self.draw_password_input()
            self.draw_on_screen_keyboard()

    def draw_network_list(self):
        """Draw the list of available networks with touch support - optimized for 1024x600."""
        font = pygame.font.Font(None, 32)  # Smaller font for compact screen
        y_start = 70  # Start higher
        item_height = 60  # Smaller touch targets for more networks

        for i, network in enumerate(self.wifi_networks[:7]):  # Show max 7 networks for compact screen
            y_pos = y_start + i * item_height

            # Create touch-friendly button for each network
            network_rect = pygame.Rect(50, y_pos - 5, self.WIDTH - 100, item_height - 10)

            # Highlight selected network
            if i == self.selected_network_index:
                color = self.BUTTON_HOVER_COLOR
            else:
                color = self.BUTTON_COLOR

            pygame.draw.rect(self.screen, color, network_rect, border_radius=10)
            pygame.draw.rect(self.screen, self.WHITE, network_rect, 3, border_radius=10)

            # Network name and info
            text = f"{network.ssid}"
            if network.is_secured:
                text += " [SECURED]"
            text += f" ({network.signal_strength}%)"

            text_surface = font.render(text, True, self.WHITE)
            text_rect = text_surface.get_rect(center=network_rect.center)
            self.screen.blit(text_surface, text_rect)

        # Touch instructions - compact positioning
        font_small = pygame.font.Font(None, 24)  # Smaller font
        instruction = "Tap a network to connect"
        text_surface = font_small.render(instruction, True, self.WHITE)
        self.screen.blit(text_surface, (50, self.HEIGHT - 30))  # Single instruction at bottom

    def handle_network_list_touch(self, pos):
        """Handle touch input on network list - optimized for 1024x600."""
        if self.wifi_input_mode != "selection":
            return

        y_start = 70  # Match draw_network_list
        item_height = 60  # Match draw_network_list

        for i, network in enumerate(self.wifi_networks[:7]):  # Match draw_network_list
            y_pos = y_start + i * item_height
            network_rect = pygame.Rect(50, y_pos - 5, self.WIDTH - 100, item_height - 10)

            if network_rect.collidepoint(pos):
                self.selected_network_index = i
                # Connect to selected network
                if network.is_secured:
                    self.wifi_input_mode = "keyboard"
                    self.wifi_password = ""
                else:
                    self.connect_to_wifi(network.ssid, "")
                return

    def draw_password_input(self):
        """Draw password input interface - optimized for 1024x600."""
        font = pygame.font.Font(None, 32)  # Smaller font for compact screen

        # Back button in top left
        back_rect = self.draw_back_button()

        # Cancel button in top right
        cancel_rect = self.draw_cancel_button()

        # Selected network info
        if self.wifi_networks and self.selected_network_index < len(self.wifi_networks):
            network = self.wifi_networks[self.selected_network_index]
            network_text = f"Connecting to: {network.ssid}"
            text_surface = font.render(network_text, True, self.WHITE)
            text_rect = text_surface.get_rect(center=(self.WIDTH // 2, 80))  # Higher position
            self.screen.blit(text_surface, text_rect)

        # Password input field
        password_label = font.render("Password:", True, self.WHITE)
        self.screen.blit(password_label, (50, 120))  # Higher position

        # Password field with show/hide toggle
        password_field_width = self.WIDTH - 200  # Leave space for show/hide button
        password_display = self.wifi_password if self.show_password else "*" * len(self.wifi_password)
        if not self.wifi_password:
            password_display = "Tap to enter password"

        password_text = font.render(password_display, True, self.WHITE if self.wifi_password else (150, 150, 150))
        password_rect = pygame.Rect(50, 150, password_field_width, 40)  # Smaller height and higher position
        pygame.draw.rect(self.screen, (40, 40, 40), password_rect)
        pygame.draw.rect(self.screen, self.WHITE, password_rect, 3)

        # Show/Hide password button
        show_hide_rect = pygame.Rect(password_rect.right + 10, 150, 80, 40)
        pygame.draw.rect(self.screen, (60, 60, 60), show_hide_rect, border_radius=5)
        pygame.draw.rect(self.screen, self.WHITE, show_hide_rect, 2, border_radius=5)

        show_hide_text = "Hide" if self.show_password else "Show"
        show_hide_surface = pygame.font.Font(None, 24).render(show_hide_text, True, self.WHITE)
        show_hide_text_rect = show_hide_surface.get_rect(center=show_hide_rect.center)
        self.screen.blit(show_hide_surface, show_hide_text_rect)

        text_rect = password_text.get_rect()
        text_rect.centery = password_rect.centery
        text_rect.x = password_rect.x + 10
        self.screen.blit(password_text, text_rect)

        # Touch instructions - compact positioning
        font_small = pygame.font.Font(None, 24)  # Smaller font
        if self.wifi_input_mode == "keyboard":
            instructions = [
                "Use on-screen keyboard below",
                "Tap ENTER to connect"
            ]
            start_y = 200  # Position above keyboard
        else:
            instructions = [
                "Tap password field to enter password",
                "Tap here to go back"
            ]
            start_y = 220  # Lower position when no keyboard

        for i, instruction in enumerate(instructions):
            text_surface = font_small.render(instruction, True, self.WHITE)
            self.screen.blit(text_surface, (50, start_y + i * 25))

    def draw_buttons(self):
        """Draw the button overlay."""
        if not self.show_buttons or not self.buttons:
            return

        current_time = pygame.time.get_ticks()

        # Calculate fade alpha
        if self.button_fade_start > 0 and current_time >= self.button_fade_start:
            fade_elapsed = current_time - self.button_fade_start
            if fade_elapsed > 1000:  # Fade out over 1 second
                self.show_buttons = False
                return

            alpha = max(0, 255 - int((fade_elapsed / 1000) * 255))
        else:
            alpha = 255

        # Create surface for buttons with alpha
        button_surface = pygame.Surface((self.WIDTH, self.HEIGHT), pygame.SRCALPHA)

        # Draw semi-transparent background
        overlay = pygame.Surface((self.WIDTH, self.HEIGHT), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 128))
        button_surface.blit(overlay, (0, 0))

        # Draw buttons with larger font for touch interface
        font = pygame.font.Font(None, 48)  # Larger font for better readability

        for button in self.buttons:
            # Button background with rounded corners effect
            color = self.BUTTON_HOVER_COLOR if button == self.hovered_button else self.BUTTON_COLOR
            pygame.draw.rect(button_surface, (*color, alpha), button['rect'], border_radius=10)
            pygame.draw.rect(button_surface, (255, 255, 255, alpha), button['rect'], 3, border_radius=10)

            # Button text
            text_surface = font.render(button['text'], True, (*self.BUTTON_TEXT_COLOR, alpha))
            text_rect = text_surface.get_rect(center=button['rect'].center)
            button_surface.blit(text_surface, text_rect)

        self.screen.blit(button_surface, (0, 0))

    def show_message(self, message: str, duration: int = 2000):
        """Show a temporary message on screen."""
        self.message_text = message
        self.message_start_time = pygame.time.get_ticks()
        self.message_duration = duration

    def draw_message(self):
        """Draw temporary message if active."""
        if not hasattr(self, 'message_text') or not self.message_text:
            return

        current_time = pygame.time.get_ticks()
        if current_time - self.message_start_time > self.message_duration:
            self.message_text = ""
            return

        font = pygame.font.Font(None, 48)
        text_surface = font.render(self.message_text, True, self.WHITE)
        text_rect = text_surface.get_rect(center=(self.WIDTH // 2, self.HEIGHT // 2))

        # Draw background
        bg_rect = text_rect.inflate(40, 20)
        pygame.draw.rect(self.screen, self.BLACK, bg_rect)
        pygame.draw.rect(self.screen, self.WHITE, bg_rect, 2)

        self.screen.blit(text_surface, text_rect)

    def draw_cancel_button(self):
        """Draw cancel button in top right corner."""
        button_size = 50
        margin = 20
        cancel_rect = pygame.Rect(self.WIDTH - button_size - margin, margin, button_size, button_size)

        # Draw button background
        pygame.draw.rect(self.screen, (200, 50, 50), cancel_rect, border_radius=8)
        pygame.draw.rect(self.screen, self.WHITE, cancel_rect, 2, border_radius=8)

        # Draw X symbol
        font = pygame.font.Font(None, 36)
        x_text = font.render("X", True, self.WHITE)
        x_rect = x_text.get_rect(center=cancel_rect.center)
        self.screen.blit(x_text, x_rect)

        return cancel_rect

    def draw_back_button(self):
        """Draw back button for password interface."""
        button_width = 80
        button_height = 40
        margin = 20
        back_rect = pygame.Rect(margin, margin, button_width, button_height)

        # Draw button background
        pygame.draw.rect(self.screen, (100, 100, 100), back_rect, border_radius=8)
        pygame.draw.rect(self.screen, self.WHITE, back_rect, 2, border_radius=8)

        # Draw back arrow and text
        font = pygame.font.Font(None, 24)
        back_text = font.render("< Back", True, self.WHITE)
        back_text_rect = back_text.get_rect(center=back_rect.center)
        self.screen.blit(back_text, back_text_rect)

        return back_rect

    def _create_keyboard_layout(self):
        """Create on-screen keyboard layout with better special keys."""
        return [
            ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
            ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
            ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
            ['z', 'x', 'c', 'v', 'b', 'n', 'm', 'DEL'],
            ['SHIFT', 'SPACE', 'SHOW', 'ENTER']
        ]

    def draw_on_screen_keyboard(self):
        """Draw touch-friendly on-screen keyboard - optimized for 1024x600."""
        if self.wifi_input_mode != "keyboard":
            return

        # Keyboard area - compact layout
        keyboard_start_y = 250  # Fixed position for compact screen
        key_width = 60  # Smaller keys
        key_height = 45  # Smaller keys
        key_spacing = 3  # Reduced spacing

        font = pygame.font.Font(None, 24)  # Smaller font for compact keyboard

        for row_idx, row in enumerate(self.keyboard_layout):
            row_width = len(row) * (key_width + key_spacing) - key_spacing
            start_x = (self.WIDTH - row_width) // 2
            y = keyboard_start_y + row_idx * (key_height + key_spacing)

            for col_idx, key in enumerate(row):
                x = start_x + col_idx * (key_width + key_spacing)

                # Adjust width for special keys - smaller multipliers for compact screen
                current_key_width = key_width
                if key in ['SPACE']:  # Space
                    current_key_width = key_width * 2.5  # Smaller space bar
                elif key in ['SHIFT', 'DEL', 'SHOW', 'ENTER']:  # Special keys
                    current_key_width = key_width * 1.3  # Smaller special keys

                # Key rectangle
                key_rect = pygame.Rect(x, y, current_key_width, key_height)

                # Key color based on function
                if row_idx == self.keyboard_selected_row and col_idx == self.keyboard_selected_col:
                    color = self.BUTTON_HOVER_COLOR
                elif key == 'SHIFT':  # Shift
                    color = (70, 130, 180) if self.keyboard_shift else (100, 100, 100)
                elif key == 'DEL':  # Backspace
                    color = (180, 70, 70)
                elif key == 'ENTER':  # Enter/Connect
                    color = (70, 180, 70)
                elif key == 'SHOW':  # Show/Hide password
                    color = (180, 140, 70)
                else:
                    color = self.BUTTON_COLOR

                # Draw key
                pygame.draw.rect(self.screen, color, key_rect, border_radius=5)
                pygame.draw.rect(self.screen, self.WHITE, key_rect, 2, border_radius=5)

                # Key text
                display_key = key
                if key == 'SPACE':
                    display_key = 'Space'
                    # Use smaller font for space key to fit text
                    special_font = pygame.font.Font(None, 20)
                    text_surface = special_font.render(display_key, True, self.WHITE)
                elif key in ['SHIFT', 'DEL', 'SHOW', 'ENTER']:
                    # Use smaller font for special keys to fit text
                    special_font = pygame.font.Font(None, 20)
                    text_surface = special_font.render(display_key, True, self.WHITE)
                elif key.isalpha() and (self.keyboard_shift or self.keyboard_caps):
                    display_key = key.upper()
                    text_surface = font.render(display_key, True, self.WHITE)
                else:
                    text_surface = font.render(display_key, True, self.WHITE)

                text_rect = text_surface.get_rect(center=key_rect.center)
                self.screen.blit(text_surface, text_rect)

    def handle_keyboard_touch(self, pos):
        """Handle touch input on on-screen keyboard - optimized for 1024x600."""
        if self.wifi_input_mode != "keyboard":
            return

        keyboard_start_y = 250  # Match draw_on_screen_keyboard
        key_width = 60  # Match draw_on_screen_keyboard
        key_height = 45  # Match draw_on_screen_keyboard
        key_spacing = 3  # Match draw_on_screen_keyboard

        for row_idx, row in enumerate(self.keyboard_layout):
            row_width = len(row) * (key_width + key_spacing) - key_spacing
            start_x = (self.WIDTH - row_width) // 2
            y = keyboard_start_y + row_idx * (key_height + key_spacing)

            for col_idx, key in enumerate(row):
                x = start_x + col_idx * (key_width + key_spacing)

                # Adjust width for special keys - match draw_on_screen_keyboard
                current_key_width = key_width
                if key in ['SPACE']:  # Space
                    current_key_width = key_width * 2.5  # Match draw method
                elif key in ['SHIFT', 'DEL', 'SHOW', 'ENTER']:  # Special keys
                    current_key_width = key_width * 1.3  # Match draw method

                key_rect = pygame.Rect(x, y, current_key_width, key_height)

                if key_rect.collidepoint(pos):
                    self.handle_keyboard_key(key)
                    return

    def handle_keyboard_key(self, key):
        """Handle keyboard key press with new special keys."""
        if key == 'DEL':  # Backspace
            self.wifi_password = self.wifi_password[:-1]
        elif key == 'SPACE':  # Space
            self.wifi_password += ' '
        elif key == 'SHIFT':  # Shift
            self.keyboard_shift = not self.keyboard_shift
        elif key == 'ENTER':  # Enter/Connect
            # Connect to network
            if self.wifi_networks and self.selected_network_index < len(self.wifi_networks):
                network = self.wifi_networks[self.selected_network_index]
                self.connect_to_wifi(network.ssid, self.wifi_password)
        elif key == 'SHOW':  # Show/Hide password
            self.show_password = not self.show_password
        elif key.isalnum():
            if self.keyboard_shift or self.keyboard_caps:
                self.wifi_password += key.upper()
                self.keyboard_shift = False  # Reset shift after use
            else:
                self.wifi_password += key.lower()

    def draw_touch_feedback(self):
        """Draw touch feedback circle."""
        if self.touch_feedback_pos is None:
            return

        current_time = pygame.time.get_ticks()
        elapsed = current_time - self.touch_feedback_time

        if elapsed > self.touch_feedback_duration:
            self.touch_feedback_pos = None
            return

        # Create expanding circle effect
        progress = elapsed / self.touch_feedback_duration
        radius = int(30 * progress)  # Expand from 0 to 30 pixels
        alpha = int(255 * (1 - progress))  # Fade out

        # Create surface for touch feedback
        feedback_surface = pygame.Surface((radius * 2 + 10, radius * 2 + 10), pygame.SRCALPHA)
        pygame.draw.circle(feedback_surface, (255, 255, 255, alpha),
                         (radius + 5, radius + 5), radius, 3)

        # Position the feedback circle
        feedback_rect = feedback_surface.get_rect(center=self.touch_feedback_pos)
        self.screen.blit(feedback_surface, feedback_rect)

    def run_display_loop(self, url: str):
        """Main display loop for QR code."""
        self.qr_url = url
        print("📱 Starting QR code display loop...")

        if not self.initialize_pygame():
            print("❌ Failed to initialize pygame, exiting QR display")
            return

        print("✅ QR code display loop initialized")

        while self.running:
            # Handle pygame events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_q or event.key == pygame.K_ESCAPE:
                        if self.display_mode == "wifi":
                            if self.wifi_input_mode == "password":
                                self.wifi_input_mode = "selection"
                            else:
                                self.display_mode = "face"
                                self.show_buttons = False
                        else:
                            self.running = False
                    elif self.display_mode == "wifi":
                        self.handle_wifi_keyboard_input(event)
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # Left mouse button
                        self.handle_mouse_down(event.pos)
                elif event.type == pygame.MOUSEBUTTONUP:
                    if event.button == 1:  # Left mouse button
                        self.handle_mouse_up(event.pos)
                elif event.type == pygame.MOUSEMOTION:
                    self.handle_mouse_motion(event.pos)

            # Draw based on current mode
            if self.display_mode == "qr":
                self.draw_qr_screen()
                self.draw_buttons()
                self.draw_touch_feedback()
                self.draw_message()
                pygame.display.flip()
                self.clock.tick(30)  # Lower FPS for QR display
            elif self.display_mode == "enrollment":
                self.draw_enrollment_qr_screen()
                self.draw_buttons()
                self.draw_touch_feedback()
                self.draw_message()
                pygame.display.flip()
                self.clock.tick(30)  # Lower FPS for QR display
            elif self.display_mode == "quiz":
                self.draw_quiz_qr_screen()
                self.draw_buttons()
                self.draw_touch_feedback()
                self.draw_message()
                pygame.display.flip()
                self.clock.tick(30)  # Lower FPS for QR display
            elif self.display_mode == "wifi":
                self.draw_wifi_interface()
                self.draw_message()
                pygame.display.flip()
                self.clock.tick(30)
            else:  # face mode
                self.draw_face_screen()
                self.draw_buttons()
                self.draw_touch_feedback()
                self.draw_message()
                pygame.display.flip()
                self.clock.tick(60)  # Higher FPS for face animation

        print("📱 QR code display loop ending, cleaning up...")
        pygame.quit()
        print("✅ QR code display cleanup complete")

    def handle_mouse_down(self, pos):
        """Handle touch/mouse button down events."""
        if self.display_mode in ["face", "qr"]:
            self.mouse_down_time = pygame.time.get_ticks()
            self.mouse_down_pos = pos
            # Add touch feedback
            self.touch_feedback_pos = pos
            self.touch_feedback_time = pygame.time.get_ticks()

    def handle_mouse_up(self, pos):
        """Handle touch/mouse button up events."""
        if self.display_mode == "wifi":
            # Handle WiFi interface touches
            if self.wifi_input_mode == "selection":
                # Check cancel button
                cancel_rect = pygame.Rect(self.WIDTH - 70, 20, 50, 50)
                if cancel_rect.collidepoint(pos):
                    self.display_mode = "face"
                    self.show_buttons = False
                    return

                self.handle_network_list_touch(pos)

            elif self.wifi_input_mode == "password":
                # Check back button
                back_rect = pygame.Rect(20, 20, 80, 40)
                if back_rect.collidepoint(pos):
                    self.wifi_input_mode = "selection"
                    return

                # Check cancel button
                cancel_rect = pygame.Rect(self.WIDTH - 70, 20, 50, 50)
                if cancel_rect.collidepoint(pos):
                    self.display_mode = "face"
                    self.show_buttons = False
                    return

                # Check show/hide password button
                password_field_width = self.WIDTH - 200
                show_hide_rect = pygame.Rect(50 + password_field_width + 10, 150, 80, 40)
                if show_hide_rect.collidepoint(pos):
                    self.show_password = not self.show_password
                    return

                # Check if tapping password field
                password_rect = pygame.Rect(50, 150, password_field_width, 40)
                if password_rect.collidepoint(pos):
                    self.wifi_input_mode = "keyboard"

            elif self.wifi_input_mode == "keyboard":
                # Check back button
                back_rect = pygame.Rect(20, 20, 80, 40)
                if back_rect.collidepoint(pos):
                    self.wifi_input_mode = "selection"
                    return

                # Check cancel button
                cancel_rect = pygame.Rect(self.WIDTH - 70, 20, 50, 50)
                if cancel_rect.collidepoint(pos):
                    self.display_mode = "face"
                    self.show_buttons = False
                    return

                self.handle_keyboard_touch(pos)

        elif self.display_mode in ["face", "qr", "enrollment", "quiz"]:
            if self.mouse_down_time > 0:
                # Check if clicking on button when buttons are visible
                if self.show_buttons:
                    for button in self.buttons:
                        if button['rect'].collidepoint(pos):
                            self.handle_button_click(button['action'])
                            return
                    # Clicked outside buttons - hide them
                    self.show_buttons = False
                else:
                    # Touch anywhere on screen - show buttons
                    self.show_buttons = True
                    self.button_fade_start = pygame.time.get_ticks() + self.button_fade_duration  # Start fade timer immediately
                    self.setup_buttons()  # Ensure buttons are set up for current mode
                    mode_name = "QR code" if self.display_mode == "qr" else ("enrollment" if self.display_mode == "enrollment" else ("quiz" if self.display_mode == "quiz" else "face"))
                    print(f"👆 Touch detected on {mode_name} - showing buttons")

                self.mouse_down_time = 0
                self.mouse_down_pos = None

    def handle_mouse_motion(self, pos):
        """Handle mouse motion events."""
        if self.display_mode in ["face", "qr"] and self.show_buttons:
            # Update hovered button
            self.hovered_button = None
            for button in self.buttons:
                if button['rect'].collidepoint(pos):
                    self.hovered_button = button
                    break

    def handle_wifi_keyboard_input(self, event):
        """Handle keyboard input for WiFi interface."""
        if self.wifi_input_mode == "selection":
            if event.key == pygame.K_UP:
                self.selected_network_index = max(0, self.selected_network_index - 1)
            elif event.key == pygame.K_DOWN:
                self.selected_network_index = min(len(self.wifi_networks) - 1, self.selected_network_index + 1)
            elif event.key == pygame.K_RETURN:
                # Connect to selected network
                network = self.wifi_networks[self.selected_network_index]
                if network.is_secured:
                    self.wifi_input_mode = "password"
                    self.wifi_password = ""
                else:
                    self.connect_to_wifi(network.ssid, "")

        elif self.wifi_input_mode == "password":
            if event.key == pygame.K_RETURN:
                # Connect with password
                network = self.wifi_networks[self.selected_network_index]
                self.connect_to_wifi(network.ssid, self.wifi_password)
            elif event.key == pygame.K_BACKSPACE:
                self.wifi_password = self.wifi_password[:-1]
            else:
                # Add character to password
                if event.unicode.isprintable():
                    self.wifi_password += event.unicode

    def connect_to_wifi(self, ssid: str, password: str):
        """Connect to WiFi network."""
        from gui.utils.wifi_manager import get_wifi_manager, restart_application

        self.show_message("Connecting...", 5000)

        # Connect in a separate thread to avoid blocking
        def connect_thread():
            wifi_manager = get_wifi_manager()
            success = wifi_manager.connect_to_network(ssid, password)

            if success:
                self.show_message("Connected! Restarting...", 2000)
                pygame.time.wait(2000)
                restart_application()
            else:
                self.show_message("Connection failed", 3000)
                self.wifi_input_mode = "selection"

        threading.Thread(target=connect_thread, daemon=True).start()

    def start(self, url: str) -> bool:
        """Start the QR code display service."""
        if self.running:
            return True

        self.running = True
        self.thread = threading.Thread(target=self.run_display_loop, args=(url,), daemon=True)
        self.thread.start()
        return True

    def stop(self):
        """Stop the QR code display service."""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2)
        self.thread = None

    def is_running(self) -> bool:
        """Check if the QR code display service is running."""
        return self.running and self.thread and self.thread.is_alive()


def setup_pygame_environment():
    """Global function to setup pygame environment variables to avoid GLX errors."""
    # Check if we're in a headless environment
    is_headless = 'SSH_CLIENT' in os.environ or 'SSH_TTY' in os.environ or os.environ.get('DISPLAY', '') == ''

    if is_headless:
        print("🔍 Detected headless/SSH environment, using software rendering")
        # For headless environments, use dummy video driver
        os.environ['SDL_VIDEODRIVER'] = 'dummy'
    else:
        # For environments with display, use X11 with software rendering
        os.environ['SDL_VIDEODRIVER'] = 'x11'
        os.environ['SDL_VIDEO_X11_FORCE_EGL'] = '0'
        os.environ['SDL_VIDEO_X11_NODIRECTCOLOR'] = '1'

    # Disable hardware acceleration that can cause GLX issues
    os.environ['SDL_RENDER_DRIVER'] = 'software'
    os.environ['SDL_FRAMEBUFFER_ACCELERATION'] = '0'

    # Additional fallback options
    if 'DISPLAY' not in os.environ:
        os.environ['DISPLAY'] = ':0'

    print(f"🔧 Pygame environment configured: SDL_VIDEODRIVER={os.environ.get('SDL_VIDEODRIVER')}")

# Global instance
_qr_display_service = None

def get_qr_display_service() -> QRDisplayService:
    """Get the global QR display service instance."""
    global _qr_display_service
    if _qr_display_service is None:
        _qr_display_service = QRDisplayService()
    return _qr_display_service

def start_qr_display(url: str) -> bool:
    """Start the QR code display service."""
    service = get_qr_display_service()
    return service.start(url)

def stop_qr_display():
    """Stop the QR code display service."""
    service = get_qr_display_service()
    service.stop()

def is_qr_display_running() -> bool:
    """Check if the QR code display service is running."""
    service = get_qr_display_service()
    return service.is_running()

def switch_to_face_display():
    """Switch from QR display to face display in the same window."""
    print("🔄 Switching from QR code to face display...")

    # Get the QR display service and tell it to switch to face mode
    service = get_qr_display_service()
    if service.is_running():
        service.switch_to_face_mode()
        print("✅ Successfully switched to face display in same window")
        return True
    else:
        print("❌ QR display not running, cannot switch")
        return False

def switch_to_enrollment_display(enrollment_url: str, class_name: str):
    """Switch to enrollment QR display mode."""
    print(f"🔄 Switching to enrollment QR display for class: {class_name}")

    # Get the QR display service and tell it to switch to enrollment mode
    service = get_qr_display_service()
    if service.is_running():
        service.switch_to_enrollment_mode(enrollment_url, class_name)
        print("✅ Successfully switched to enrollment QR display")
        return True
    else:
        print("❌ QR display not running, cannot switch to enrollment mode")
        return False

def switch_back_to_face_from_enrollment():
    """Switch back to face display from enrollment mode."""
    print("🔄 Switching back to face display from enrollment...")

    # Get the QR display service and tell it to switch back to face mode
    service = get_qr_display_service()
    if service.is_running():
        service.switch_back_to_face_from_enrollment()
        print("✅ Successfully switched back to face display")
        return True
    else:
        print("❌ QR display not running, cannot switch back")
        return False

def switch_to_quiz_display(quiz_url: str, quiz_title: str):
    """Switch to quiz QR display mode."""
    print(f"🔄 Switching to quiz QR display for quiz: {quiz_title}")

    # Get the QR display service and tell it to switch to quiz mode
    service = get_qr_display_service()
    if service.is_running():
        service.switch_to_quiz_mode(quiz_url, quiz_title)
        print("✅ Successfully switched to quiz QR display")
        return True
    else:
        print("❌ QR display not running, cannot switch to quiz mode")
        return False

def switch_back_to_face_from_quiz():
    """Switch back to face display from quiz mode."""
    print("🔄 Switching back to face display from quiz...")

    # Get the QR display service and tell it to switch back to face mode
    service = get_qr_display_service()
    if service.is_running():
        service.switch_back_to_face_from_quiz()
        print("✅ Successfully switched back to face display")
        return True
    else:
        print("❌ QR display not running, cannot switch back")
        return False
