# Teacher Assistant UI

This is the UI component of the Teacher Assistant application, built with the Flet framework.

## Project Structure

- `gui/app.py`: Main application entry point
- `gui/config/`: Configuration files
  - `constants.py`: Application constants
  - `language.py`: Language support
  - `theme.py`: Theme management
- `gui/state/`: Application state management
  - `app_state.py`: State management class
- `gui/components/`: Reusable UI components
  - `app_bar.py`: Application bar component
  - `cards.py`: Card components (dashboard, class, student, subject)
  - `dialogs.py`: Dialog components
  - `layout.py`: Layout components
  - `navigation.py`: Navigation components
  - `utils.py`: Utility functions
- `gui/services/`: Business logic services
  - `attendance_service.py`: Attendance management
  - `class_service.py`: Class management
  - `enrollment_service.py`: Student enrollment via QR code
  - `student_service.py`: Student management
  - `subject_service.py`: Subject management
  - `video_streaming_service.py`: Video streaming for attendance
- `gui/utils/`: Utility functions
  - `connection.py`: Database connection utilities
  - `network.py`: Network utilities
  - `qr_code.py`: QR code generation and handling
  - `video_stream.py`: Video streaming utilities
- `gui/views/`: Page views
  - `dashboard.py`: Dashboard view
  - `classes.py`: Class management view
  - `students.py`: Student management view
  - `subjects.py`: Subject management view
  - `quizzes.py`: Quiz management view
  - `settings.py`: Settings view

## Architecture

The application follows a modular architecture with clear separation of concerns:

1. **Views**: Responsible for creating the UI for each page
2. **Components**: Reusable UI components used across views
3. **Services**: Business logic that interacts with the backend
4. **State**: Application state management
5. **Config**: Configuration and theming
6. **Utils**: Utility functions

## Features

- Dashboard with statistics
- Class management (create, view, edit, delete)
- Student management (enroll, view, update, delete)
- Subject management (create, view, edit, delete)
- Quiz management (create, edit, delete quizzes with QR codes)
- Attendance taking within class management
- QR code generation for enrollment and quizzes

## How to Run

To run the application:

```bash
python main.py
```

## Dependencies

- Flet: UI framework
- Facial Recognition System: Backend for face recognition and attendance tracking
- Quiz Management System: Backend for quiz creation and management
