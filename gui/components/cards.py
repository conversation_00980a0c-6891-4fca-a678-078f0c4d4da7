"""
Card components for the application.
"""
import flet as ft
from gui.config.language import get_text
# Import icons directly from flet
ICON_PERSON = ft.Icons.PERSON
ICON_BOOK = ft.Icons.BOOK
ICON_MORE = ft.Icons.MORE_VERT
ICON_EDIT = ft.Icons.EDIT
ICON_DELETE = ft.Icons.DELETE
ICON_PERSON_ADD = ft.Icons.PERSON_ADD
ICON_ATTENDANCE = ft.Icons.FACT_CHECK
ICON_ATTENDANCE_HISTORY = ft.Icons.HISTORY
ICON_FACE = ft.Icons.FACE
ICON_QR_CODE = ft.Icons.QR_CODE

def create_dashboard_card(title: str, value: str, button_text: str, on_click, icon=None, page=None):
    """
    Create a dashboard card.

    Args:
        title: The card title
        value: The card value
        button_text: The button text
        on_click: The function to call when the button is clicked
        icon: The icon to display (optional)
        page: The Flet page object (optional, for responsive design)

    Returns:
        ft.Card: The dashboard card
    """
    # Adjust sizes for mobile
    is_mobile = page and getattr(page, 'is_mobile', False)
    title_size = 14 if is_mobile else 16
    value_size = 32 if is_mobile else 40
    card_width = page.width*0.75 if is_mobile else 250
    padding = 12 if is_mobile else 20

    card_content = [
        ft.Text(title, size=title_size, weight=ft.FontWeight.BOLD),
        ft.Text(value, size=value_size, weight=ft.FontWeight.BOLD),
        ft.ElevatedButton(
            button_text,
            on_click=on_click,
            icon=icon,
            # Make button more compact on mobile
            style=ft.ButtonStyle(
                padding=ft.padding.all(10) if is_mobile else None
            ) if is_mobile else None
        )
    ]

    return ft.Card(
        content=ft.Container(
            content=ft.Column(card_content),
            padding=padding,
            width=card_width,
        ),
        elevation=3,
    )

def create_class_card(class_name: str, student_count: int, class_id: str, on_view, on_rename, on_delete, on_assign, on_attendance, on_qr_code=None, on_attendance_history=None, page=None):
    from gui.config.language import get_text

    current_language = getattr(page, 'language', 'fr') if page else 'fr'

    menu_items = [
        ft.PopupMenuItem(text=get_text("view_students", current_language), icon=ICON_PERSON, on_click=on_view),
        ft.PopupMenuItem(text=get_text("assign_students", current_language), icon=ICON_PERSON_ADD, on_click=on_assign),
        ft.PopupMenuItem(text=get_text("take_attendance_simple", current_language), icon=ICON_ATTENDANCE, on_click=on_attendance),
        ft.PopupMenuItem(text=get_text("rename_class_simple", current_language), icon=ICON_EDIT, on_click=on_rename),
        ft.PopupMenuItem(text=get_text("delete_class_simple", current_language), icon=ICON_DELETE, on_click=on_delete),
    ]

    if on_qr_code:
        menu_items.insert(2, ft.PopupMenuItem(text=get_text("qr_enrollment", current_language), icon=ICON_QR_CODE, on_click=on_qr_code))

    if on_attendance_history:
        menu_items.insert(-2, ft.PopupMenuItem(text=get_text("attendance_history", current_language), icon=ICON_ATTENDANCE_HISTORY, on_click=on_attendance_history))

    # Format student count with proper French grammar
    if student_count == 0:
        student_text = get_text("no_students", current_language)
    elif student_count == 1:
        student_text = f"1 {get_text('student_singular', current_language)}"
    else:
        student_text = f"{student_count} {get_text('students_plural', current_language)}"

    return ft.Card(
        content=ft.ListTile(
            title=ft.Text(class_name, weight=ft.FontWeight.BOLD),
            subtitle=ft.Text(student_text),
            trailing=ft.PopupMenuButton(
                icon=ICON_MORE,
                items=menu_items,
            ),
        ),
        elevation=1,
        margin=ft.margin.all(4),
    )

def create_student_card(student_name: str, student_id: str, class_name: str = None, on_edit=None, on_delete=None, on_update_face=None, page=None):
    """
    Create a student card.

    Args:
        student_name: The student name
        student_id: The student ID
        class_name: The class name (optional)
        on_edit: The function to call when the user clicks "Edit Student"
        on_delete: The function to call when the user clicks "Delete Student"
        on_update_face: The function to call when the user clicks "Update Face Encoding"
        page: The Flet page object (optional, for responsive design)

    Returns:
        ft.Card: The student card
    """
    from gui.config.language import get_text

    # Adjust sizes for mobile
    is_mobile = page and getattr(page, 'is_mobile', False)
    title_size = 14 if is_mobile else 16
    card_width = 320 if is_mobile else 500
    padding = 8 if is_mobile else 10
    current_language = getattr(page, 'language', 'fr') if page else 'fr'

    menu_items = []

    if on_edit:
        menu_items.append(
            ft.PopupMenuItem(
                text=get_text("edit_student", current_language),
                icon=ICON_EDIT,
                on_click=on_edit
            )
        )

    if on_delete:
        menu_items.append(
            ft.PopupMenuItem(
                text=get_text("delete_student", current_language),
                icon=ICON_DELETE,
                on_click=on_delete
            )
        )

    if on_update_face:
        menu_items.append(
            ft.PopupMenuItem(
                text=get_text("update_face_encoding", current_language),
                icon=ICON_FACE,
                on_click=on_update_face
            )
        )

    subtitle_text = f"{get_text('class', current_language)}: {class_name}" if class_name else get_text("not_assigned_to_class", current_language)

    return ft.Card(
        content=ft.Container(
            content=ft.ListTile(
                leading=ft.Icon(ICON_PERSON),
                title=ft.Text(student_name, size=title_size, weight=ft.FontWeight.BOLD),
                subtitle=ft.Text(subtitle_text),
                trailing=ft.PopupMenuButton(
                    icon=ICON_MORE,
                    items=menu_items
                ) if menu_items else None,
            ),
            padding=padding,
            width=card_width,
        ),
        elevation=2,
    )

def create_subject_card(subject_name: str, subject_id: str, class_name: str, teacher_name: str = None, on_edit=None, on_delete=None, page=None):
    from gui.config.language import get_text

    current_language = getattr(page, 'language', 'fr') if page else 'fr'
    menu_items = []

    if on_edit:
        menu_items.append(
            ft.PopupMenuItem(
                text=get_text("edit_subject", current_language),
                icon=ICON_EDIT,
                on_click=on_edit
            )
        )

    if on_delete:
        menu_items.append(
            ft.PopupMenuItem(
                text=get_text("delete_subject", current_language),
                icon=ICON_DELETE,
                on_click=on_delete
            )
        )

    subtitle_text = f"{get_text('class', current_language)}: {class_name}"

    return ft.Card(
        content=ft.ListTile(
            title=ft.Text(subject_name, weight=ft.FontWeight.BOLD),
            subtitle=ft.Text(subtitle_text),
            trailing=ft.PopupMenuButton(
                icon=ICON_MORE,
                items=menu_items
            ) if menu_items else None,
        ),
        elevation=1,
        margin=ft.margin.all(4),
    )
