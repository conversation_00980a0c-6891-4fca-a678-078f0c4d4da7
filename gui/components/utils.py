"""
Utility functions for the application.
"""
import flet as ft
from gui.config.constants import COLOR_ERROR, COLOR_SUCCESS

def create_loading_indicator():
    """
    Create a loading indicator.

    Returns:
        ft.ProgressRing: The loading indicator
    """
    return ft.ProgressRing()

def create_error_text(message):
    """
    Create an error text.

    Args:
        message: The error message

    Returns:
        ft.Text: The error text
    """
    return ft.Text(message, color=COLOR_ERROR)

def create_success_text(message):
    """
    Create a success text.

    Args:
        message: The success message

    Returns:
        ft.Text: The success text
    """
    return ft.Text(message, color=COLOR_SUCCESS)

