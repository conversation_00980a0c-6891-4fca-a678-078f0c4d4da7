"""
AppBar component for the application.
"""
import flet as ft
from gui.config.theme import get_theme_icon_button
from gui.config.constants import ICON_MENU, ICON_SETTINGS, ROUTE_SETTINGS
from gui.config.language import get_text

# Store references to the navigation drawer and overlay
_nav_drawer = None
_overlay = None

def toggle_drawer(page):
    """
    Toggle the navigation drawer visibility.

    Args:
        page: The Flet page object
    """
    global _nav_drawer, _overlay
    if _nav_drawer and _overlay:
        is_visible = not _nav_drawer.visible
        _nav_drawer.visible = is_visible
        _overlay.visible = is_visible
        page.update()

def create_app_bar(page: ft.Page, title: str = None):
    """
    Create an application bar.

    Args:
        page: The Flet page object
        title: The title to display in the app bar

    Returns:
        ft.AppBar: The application bar
    """
    current_language = getattr(page, 'language', 'fr')
    if title is None:
        title = get_text("app_name", current_language)

    theme_icon = get_theme_icon_button(page)

    # Create settings button
    def on_settings_click(_):
        print(get_text("settings_button_clicked", current_language))
        page.go(ROUTE_SETTINGS)

    settings_button = ft.IconButton(
        icon=ICON_SETTINGS,
        tooltip=get_text("settings_tooltip", current_language),
        on_click=on_settings_click
    )

    # Create actions list
    actions = [settings_button, theme_icon]
    leading = None

    # Add menu button for mobile
    if getattr(page, 'is_mobile', False):
        # Create a menu button for mobile
        menu_button = ft.IconButton(
            icon=ICON_MENU,
            tooltip=get_text("menu", current_language),
            on_click=lambda _: toggle_drawer(page)
        )
        leading = menu_button

        # Create navigation drawer for mobile
        from gui.components.navigation import create_navigation_drawer
        global _nav_drawer
        _nav_drawer = create_navigation_drawer(page)

        # Create a semi-transparent overlay for the drawer
        global _overlay

        # Use safe dimensions that work on all platforms
        safe_width = 1200  # Default width if window_width is not available
        safe_height = 800  # Default height if window_height is not available
        try:
            if hasattr(page, 'window_width') and page.window_width:
                safe_width = page.window_width
            if hasattr(page, 'window_height') and page.window_height:
                safe_height = page.window_height
        except Exception:
            pass

        _overlay = ft.Container(
            bgcolor=ft.Colors.with_opacity(0.5, ft.Colors.BLACK),
            width=safe_width,
            height=safe_height,
            visible=False,  # Start hidden
            on_click=lambda _: toggle_drawer(page)  # Close drawer when clicking outside
        )

        # Add drawer and overlay to the page
        drawer_exists = False
        for control in page.overlay:
            if isinstance(control, ft.Container) and control == _nav_drawer:
                drawer_exists = True
                break

        if not drawer_exists:
            # Add the overlay and drawer to the page overlay
            page.overlay.append(_overlay)  # Add overlay first (behind drawer)
            page.overlay.append(_nav_drawer)  # Add drawer on top
            page.update()

    return ft.AppBar(
        leading=leading,
        title=ft.Text(title),
        center_title=True,
        bgcolor=ft.Colors.BLUE_GREY_200,
        actions=actions,
    )
