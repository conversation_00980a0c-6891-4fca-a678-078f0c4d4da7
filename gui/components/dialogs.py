"""
Dialog components for the application.
"""
import flet as ft
from gui.config.constants import COLOR_ERROR
from gui.config.language import get_text, DEFAULT_LANGUAGE

def create_confirmation_dialog(
    page: ft.Page,
    title: str,
    content: str,
    on_confirm,
    confirm_text: str = None,
    cancel_text: str = None,
    confirm_color = None,
    is_destructive: bool = False
):
    """
    Create a confirmation dialog.

    Args:
        page: The Flet page object
        title: The dialog title
        content: The dialog content
        on_confirm: The function to call when the user confirms
        confirm_text: The text for the confirm button
        cancel_text: The text for the cancel button
        confirm_color: The color for the confirm button
        is_destructive: Whether the action is destructive

    Returns:
        ft.AlertDialog: The confirmation dialog
    """
    current_language = getattr(page, 'language', DEFAULT_LANGUAGE)

    if confirm_text is None:
        confirm_text = get_text("confirm", current_language)
    if cancel_text is None:
        cancel_text = get_text("cancel", current_language)

    if is_destructive:
        confirm_color = COLOR_ERROR

    def close_dialog(_):
        page.dialog.open = False
        page.update()

    def handle_confirm(_):
        close_dialog(_)
        on_confirm()

    dialog = ft.AlertDialog(
        title=ft.Text(title),
        content=ft.Text(content),
        actions=[
            ft.TextButton(cancel_text, on_click=close_dialog),
            ft.TextButton(
                confirm_text,
                on_click=handle_confirm,
                style=ft.ButtonStyle(color=confirm_color) if confirm_color else None
            ),
        ],
    )

    return dialog

def create_form_dialog(
    page: ft.Page,
    title: str,
    form_controls,
    on_submit,
    submit_text: str = None,
    cancel_text: str = None,
):
    """
    Create a form dialog.

    Args:
        page: The Flet page object
        title: The dialog title
        form_controls: The form controls to display
        on_submit: The function to call when the user submits the form
        submit_text: The text for the submit button
        cancel_text: The text for the cancel button

    Returns:
        ft.AlertDialog: The form dialog
    """
    current_language = getattr(page, 'language', DEFAULT_LANGUAGE)

    if submit_text is None:
        submit_text = get_text("submit", current_language)
    if cancel_text is None:
        cancel_text = get_text("cancel", current_language)

    def close_dialog(_):
        page.dialog.open = False
        page.update()

    def handle_submit(_):
        close_dialog(_)
        on_submit()

    dialog = ft.AlertDialog(
        title=ft.Text(title),
        content=ft.Column(
            form_controls,
            scroll=ft.ScrollMode.AUTO,
            height=400,
            width=400,
        ),
        actions=[
            ft.TextButton(cancel_text, on_click=close_dialog),
            ft.TextButton(submit_text, on_click=handle_submit),
        ],
    )

    return dialog

def show_dialog(page: ft.Page, dialog: ft.AlertDialog):
    """
    Show a dialog.

    Args:
        page: The Flet page object
        dialog: The dialog to show
    """
    page.dialog = dialog
    dialog.open = True
    page.views[0].controls.append(dialog)
    page.update()

def close_dialog(page: ft.Page):
    """
    Close the current dialog.

    Args:
        page: The Flet page object
    """
    if page.dialog:
        page.dialog.open = False
        page.update()
