"""
Settings dialog component for the application.
"""
import flet as ft
from gui.config.language import get_text, LANGUAGE_ENGLISH, LANGUAGE_NAMES
from gui.config.theme import toggle_theme

def show_settings_dialog(page: ft.Page):
    """
    Show the settings dialog.

    Args:
        page: The Flet page object
    """
    current_language = getattr(page, 'language', LANGUAGE_ENGLISH)
    print(get_text("opening_settings_dialog", current_language))

    # Create a simple settings page instead of a dialog
    # This is more reliable than using dialogs

    def close_settings(_):
        # Remove the settings page from the stack
        if len(page.views) > 1:
            page.views.pop()
            page.update()

    def save_settings(_):
        # Get selected language
        selected_language = language_dropdown.value

        # Update language if changed
        if selected_language != current_language:
            if hasattr(page, 'app_state'):
                page.app_state.set_language(selected_language)
            else:
                # Fallback
                page.language = selected_language

        # Update theme if changed
        new_theme = ft.ThemeMode.DARK if dark_mode_switch.value else ft.ThemeMode.LIGHT
        if new_theme != page.theme_mode:
            toggle_theme(page)

        # Close settings
        close_settings(None)

    # Create language dropdown
    language_dropdown = ft.Dropdown(
        label=get_text("select_language", current_language),
        value=current_language,
        options=[
            ft.dropdown.Option(key=lang_code, text=name)
            for lang_code, name in LANGUAGE_NAMES.items()
        ],
        width=250
    )

    # Create theme switch
    is_dark = page.theme_mode == ft.ThemeMode.DARK
    dark_mode_switch = ft.Switch(
        value=is_dark,
        label=get_text("dark_mode" if is_dark else "light_mode", current_language)
    )

    # Create settings view
    settings_view = ft.View(
        "/settings",
        [
            # App bar
            ft.AppBar(
                title=ft.Text(get_text("settings", current_language)),
                center_title=True,
                bgcolor=ft.Colors.BLUE_GREY_200,
                leading=ft.IconButton(
                    icon=ft.Icons.ARROW_BACK,
                    on_click=close_settings
                ),
            ),

            # Settings content
            ft.Container(
                content=ft.Column([
                    # Language section
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                get_text("language", current_language),
                                size=18,
                                weight=ft.FontWeight.BOLD
                            ),
                            language_dropdown,
                        ]),
                        padding=ft.padding.all(20),
                        margin=ft.margin.only(bottom=10),
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.OUTLINE_VARIANT),
                    ),

                    # Theme section
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                get_text("theme", current_language),
                                size=18,
                                weight=ft.FontWeight.BOLD
                            ),
                            dark_mode_switch,
                        ]),
                        padding=ft.padding.all(20),
                        margin=ft.margin.only(bottom=20),
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.OUTLINE_VARIANT),
                    ),

                    # Save button
                    ft.ElevatedButton(
                        text=get_text("save", current_language),
                        on_click=save_settings,
                        width=200,
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=10),
                        ),
                    ),
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=10),
                padding=ft.padding.all(20),
                alignment=ft.alignment.top_center,
                expand=True,
            ),
        ],
        padding=0,
        spacing=0,
    )

    # Add the settings view
    page.views.append(settings_view)
    page.update()
