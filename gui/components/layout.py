"""
Layout components for the application.
"""
import flet as ft
from gui.components.navigation import create_navigation_rail
from gui.components.app_bar import create_app_bar
from gui.components.breadcrumbs import create_breadcrumb

def create_page_layout(page: ft.Page, title: str, content, actions=None):
    """
    Create a page layout with navigation rail and app bar.

    Args:
        page: The Flet page object
        title: The page title
        content: The page content
        actions: Additional actions to display (optional)

    Returns:
        ft.View: The page view
    """
    # Create app bar with menu button for mobile
    app_bar = create_app_bar(page, title)

    # Create navigation rail
    nav_rail = create_navigation_rail(page)

    # Create action buttons if provided
    action_row = None
    if actions:
        action_row = ft.Container(
            content=ft.Row(
                actions,
                alignment=ft.MainAxisAlignment.END,
                wrap=True  # Allow wrapping on small screens
            ),
            margin=ft.margin.only(bottom=20)
        )

    # Create content column
    # Adjust text size for mobile
    title_size = 24 if getattr(page, 'is_mobile', False) else 30
    is_mobile = getattr(page, 'is_mobile', False)

    content_column = []

    # Add breadcrumb navigation (skip for dashboard and start page)
    if page.route not in ['/', '/dashboard'] and not is_mobile:
        content_column.append(create_breadcrumb(page, page.route))

    # Add title if provided
    if title:
        content_column.append(
            ft.Container(
                content=ft.Text(title, size=title_size, weight=ft.FontWeight.BOLD),
                margin=ft.margin.only(bottom=20, top=20 if not content_column else 10)
            )
        )

    if action_row:
        content_column.append(action_row)

    if isinstance(content, list):
        content_column.extend(content)
    else:
        content_column.append(content)

    # Create main content area with scroll
    # Adjust alignment for mobile - start from top instead of center
    is_mobile = getattr(page, 'is_mobile', False)
    main_content = ft.Column(
        content_column,
        expand=True,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        alignment=ft.MainAxisAlignment.START if is_mobile else ft.MainAxisAlignment.CENTER,
        scroll=ft.ScrollMode.AUTO  # Enable scrolling for content
    )

    # Create the layout based on platform
    if getattr(page, 'is_mobile', False):
        # For mobile: Use a simpler layout without the navigation rail
        # The drawer will be added by the app_bar component
        return ft.View(
            page.route,
            [
                app_bar,
                ft.Container(
                    content=main_content,
                    expand=True,
                    padding=ft.padding.all(10)
                )
            ],
            padding=0,
            spacing=0
        )
    else:
        # For desktop: Use the standard layout with navigation rail
        return ft.View(
            page.route,
            [
                app_bar,
                ft.Row([
                    nav_rail,
                    ft.VerticalDivider(width=1),
                    main_content
                ], expand=True)
            ],
            padding=0,
            spacing=0
        )
