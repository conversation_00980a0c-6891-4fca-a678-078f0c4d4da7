[project]
name = "teacher-assistant"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "face-recognition>=1.3.0",
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "opencv-python>=4.8.0",
    "python-dotenv>=1.0.0",
    "flet[all]>=0.27.6",
    "setuptools>=80.3.1",
    "flask>=3.1.0",
    "tiny-url>=1.0.1",
    "pygame>=2.5.0",
    "qrcode[pil]>=7.4.0",
    "Pillow>=10.0.0",
]
