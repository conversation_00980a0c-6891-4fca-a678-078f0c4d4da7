#!/usr/bin/env python3
"""
Migration script to add teacher ownership to existing data.
This script will:
1. Add teacher_id columns to existing tables if they don't exist
2. Create a default teacher for existing data
3. Associate all existing data with the default teacher
"""

import sqlite3
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from facial_recognition_system.config import DB_FILE
from gui.services.auth_service import AuthService

def get_connection():
    """Get database connection"""
    conn = sqlite3.connect(DB_FILE)
    conn.row_factory = sqlite3.Row
    conn.execute('PRAGMA foreign_keys = ON')
    return conn

def migrate_database():
    """Migrate the database to add teacher ownership"""
    print("🔄 Starting database migration for teacher ownership...")
    
    conn = get_connection()
    cursor = conn.cursor()
    
    try:
        # Check if teacher_id column exists in classes table
        cursor.execute("PRAGMA table_info(classes)")
        classes_columns = [column[1] for column in cursor.fetchall()]
        
        if 'teacher_id' not in classes_columns:
            print("📝 Adding teacher_id column to classes table...")
            cursor.execute("ALTER TABLE classes ADD COLUMN teacher_id INTEGER REFERENCES users(id)")
            
        # Check if teacher_id column exists in matieres table
        cursor.execute("PRAGMA table_info(matieres)")
        matieres_columns = [column[1] for column in cursor.fetchall()]
        
        if 'teacher_id' not in matieres_columns:
            print("📝 Adding teacher_id column to matieres table...")
            cursor.execute("ALTER TABLE matieres ADD COLUMN teacher_id INTEGER REFERENCES users(id)")
            
        # Check if teacher_id column exists in quiz table
        cursor.execute("PRAGMA table_info(quiz)")
        quiz_columns = [column[1] for column in cursor.fetchall()]
        
        if 'teacher_id' not in quiz_columns:
            print("📝 Adding teacher_id column to quiz table...")
            cursor.execute("ALTER TABLE quiz ADD COLUMN teacher_id INTEGER REFERENCES users(id)")
        
        # Initialize auth service to ensure users table exists
        auth_service = AuthService()
        
        # Get or create a default teacher for existing data
        cursor.execute("SELECT id FROM users WHERE role = 'teacher' LIMIT 1")
        teacher_row = cursor.fetchone()
        
        if not teacher_row:
            print("👨‍🏫 Creating default teacher account for existing data...")
            # Create a default teacher account
            success = auth_service.create_teacher(
                username="teacher_default",
                password="teacher123",
                full_name="Enseignant par Défaut",
                email="<EMAIL>"
            )
            
            if success:
                cursor.execute("SELECT id FROM users WHERE username = 'teacher_default'")
                teacher_row = cursor.fetchone()
            else:
                print("❌ Failed to create default teacher account")
                return False
        
        default_teacher_id = teacher_row['id']
        print(f"✅ Using teacher ID {default_teacher_id} for existing data")
        
        # Update existing classes to belong to default teacher
        cursor.execute("UPDATE classes SET teacher_id = ? WHERE teacher_id IS NULL", (default_teacher_id,))
        classes_updated = cursor.rowcount
        print(f"📚 Updated {classes_updated} classes with teacher ownership")
        
        # Update existing subjects to belong to default teacher
        cursor.execute("UPDATE matieres SET teacher_id = ? WHERE teacher_id IS NULL", (default_teacher_id,))
        subjects_updated = cursor.rowcount
        print(f"📖 Updated {subjects_updated} subjects with teacher ownership")
        
        # Update existing quizzes to belong to default teacher
        cursor.execute("UPDATE quiz SET teacher_id = ? WHERE teacher_id IS NULL", (default_teacher_id,))
        quizzes_updated = cursor.rowcount
        print(f"📝 Updated {quizzes_updated} quizzes with teacher ownership")
        
        # Update unique constraints for classes table
        print("🔧 Updating unique constraints...")
        
        # For classes table, we need to recreate it with the new constraint
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS classes_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                teacher_id INTEGER,
                created_at TEXT NOT NULL,
                updated_at TEXT,
                FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE SET NULL,
                UNIQUE(name, teacher_id)
            )
        """)
        
        # Copy data from old table to new table
        cursor.execute("""
            INSERT INTO classes_new (id, name, description, teacher_id, created_at, updated_at)
            SELECT id, name, description, teacher_id, created_at, updated_at FROM classes
        """)
        
        # Drop old table and rename new table
        cursor.execute("DROP TABLE classes")
        cursor.execute("ALTER TABLE classes_new RENAME TO classes")
        
        # For matieres table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS matieres_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                class_id INTEGER,
                teacher_id INTEGER,
                created_at TEXT NOT NULL,
                updated_at TEXT,
                FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
                FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE SET NULL,
                UNIQUE(name, class_id, teacher_id)
            )
        """)
        
        # Copy data from old table to new table
        cursor.execute("""
            INSERT INTO matieres_new (id, name, description, class_id, teacher_id, created_at, updated_at)
            SELECT id, name, description, class_id, teacher_id, created_at, updated_at FROM matieres
        """)
        
        # Drop old table and rename new table
        cursor.execute("DROP TABLE matieres")
        cursor.execute("ALTER TABLE matieres_new RENAME TO matieres")
        
        conn.commit()
        print("✅ Database migration completed successfully!")
        
        # Print summary
        print("\n📊 Migration Summary:")
        print(f"   • Classes updated: {classes_updated}")
        print(f"   • Subjects updated: {subjects_updated}")
        print(f"   • Quizzes updated: {quizzes_updated}")
        print(f"   • Default teacher ID: {default_teacher_id}")
        print("\n🔐 Default teacher credentials:")
        print("   • Username: teacher_default")
        print("   • Password: teacher123")
        print("\n⚠️  Note: All existing data now belongs to the default teacher.")
        print("   You can create additional teacher accounts through the admin interface.")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("🚀 Teacher Ownership Migration Script")
    print("=" * 50)
    
    # Check if database file exists
    if not os.path.exists(DB_FILE):
        print(f"❌ Database file not found: {DB_FILE}")
        print("Please run the application first to create the database.")
        sys.exit(1)
    
    # Run migration
    success = migrate_database()
    
    if success:
        print("\n🎉 Migration completed successfully!")
        print("You can now run the application with teacher-specific data access.")
    else:
        print("\n💥 Migration failed!")
        print("Please check the error messages above and try again.")
        sys.exit(1)
