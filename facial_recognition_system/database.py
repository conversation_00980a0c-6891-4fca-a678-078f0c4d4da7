from facial_recognition_system.local_database import (
    get_existing_records as local_get_existing_records,
    add_person_to_database as local_add_person_to_database,
    remove_person_from_database as local_remove_person_from_database,
    update_person_in_database as local_update_person_in_database,
    save_attendance_to_database as local_save_attendance_to_database
)

def get_existing_records():
    return local_get_existing_records()

def add_person_to_database(name, encodings, class_id=None):
    return local_add_person_to_database(name, encodings, class_id)

def remove_person_from_database(name):
    return local_remove_person_from_database(name)

def update_person_in_database(name, encodings):
    return local_update_person_in_database(name, encodings)

def save_attendance_to_database(attendance_data, class_id=None, session_date=None, subject_id=None):
    return local_save_attendance_to_database(attendance_data, class_id, session_date, subject_id)
