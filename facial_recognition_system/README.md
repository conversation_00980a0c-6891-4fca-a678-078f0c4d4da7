# Facial Recognition Attendance System

This is a facial recognition-based attendance system that uses face recognition to track student attendance.

## Project Structure

The project is organized into the following files:

- `config.py`: Configuration settings and environment variables
- `database.py`: Database operations for storing and retrieving student data
- `face_processing.py`: Face detection and recognition functions
- `local_database.py`: SQLite database implementation

## How to Run

This module is used as a backend for the GUI application. To run the full application, use:

```bash
python main.py
```

## Requirements

- face_recognition
- numpy
- pandas
- opencv-python (cv2)
- supabase
- python-dotenv

## Directory Structure

The system expects the following directories:

- `dataset/data`: Contains subdirectories for each student with their face images
- `dataset/test`: Contains test images for attendance tracking
